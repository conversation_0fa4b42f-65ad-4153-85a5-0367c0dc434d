{"__meta": {"id": "Xd8290d036a072c68957fd0149c995b99", "datetime": "2025-06-04 15:30:39", "utime": **********.533382, "method": "GET", "uri": "/Admin-panel/logout", "ip": "::1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749047436.590816, "end": **********.533409, "duration": 2.9425930976867676, "duration_str": "2.94s", "measures": [{"label": "Booting", "start": 1749047436.590816, "relative_start": 0, "end": **********.125162, "relative_end": **********.125162, "duration": 2.534345865249634, "duration_str": "2.53s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.125182, "relative_start": 2.5343658924102783, "end": **********.533412, "relative_end": 2.86102294921875e-06, "duration": 0.4082300662994385, "duration_str": "408ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 29918224, "peak_usage_str": "29MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET logout", "middleware": "web", "controller": "App\\Http\\Controllers\\LoginController@logout", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "logout", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FLoginController.php&line=403\" onclick=\"\">app/Http/Controllers/LoginController.php:403-422</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.32337, "accumulated_duration_str": "323ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `business_settings` where `key` = 'system_language' limit 1", "type": "query", "params": [], "bindings": ["system_language"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "middleware", "name": "localization", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\Localization.php", "line": 24}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 78}], "start": **********.162512, "duration": 0.*****************, "duration_str": "108ms", "memory": 0, "memory_str": null, "filename": "localization:24", "source": "middleware::localization:24", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FMiddleware%2FLocalization.php&line=24", "ajax": false, "filename": "Localization.php", "line": "24"}, "connection": "dease_test", "start_percent": 0, "width_percent": 33.324}, {"sql": "select * from `admins` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/LoginController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\LoginController.php", "line": 414}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.287914, "duration": 0.10732, "duration_str": "107ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "dease_test", "start_percent": 33.324, "width_percent": 33.188}, {"sql": "select `key`, `value` from `data_settings` where `key` in ('store_employee_login_url', 'store_login_url', 'admin_employee_login_url', 'admin_login_url')", "type": "query", "params": [], "bindings": ["store_employee_login_url", "store_login_url", "admin_employee_login_url", "admin_login_url"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\CentralLogics\\helpers.php", "line": 3342}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/LoginController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\LoginController.php", "line": 417}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.4028862, "duration": 0.*****************, "duration_str": "108ms", "memory": 0, "memory_str": null, "filename": "helpers.php:3342", "source": "app/CentralLogics/helpers.php:3342", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FCentralLogics%2Fhelpers.php&line=3342", "ajax": false, "filename": "helpers.php", "line": "3342"}, "connection": "dease_test", "start_percent": 66.512, "width_percent": 33.488}]}, "models": {"data": {"App\\Models\\BusinessSetting": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FBusinessSetting.php&line=1", "ajax": false, "filename": "BusinessSetting.php", "line": "?"}}, "App\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VLPBp8OZ5DPQQjor5cQrLq4YgrNA8oclmzN41QBj", "landing_site_direction": "ltr", "six_captcha": "m6Ydd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/Admin-panel/logout\"\n]", "site_direction": "ltr", "dash_params": "array:6 [\n  \"zone_id\" => \"all\"\n  \"module_id\" => 1\n  \"statistics_type\" => \"overall\"\n  \"user_overview\" => \"overall\"\n  \"commission_overview\" => \"this_year\"\n  \"business_overview\" => \"overall\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/logout", "status_code": "<pre class=sf-dump id=sf-dump-307074118 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-307074118\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1454266825 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1454266825\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1022211375 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1022211375\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/Admin-panel/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">en-US,en;q=0.9,fr-DZ;q=0.8,fr;q=0.7,ar-DZ;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1274 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ik5jRlo5KzllMDgvcERoQklrT3VUYkE9PSIsInZhbHVlIjoiVVFoUkF1aUFCQ1pIQjcxVWRKN0Y0bnJabkgrWFEwckhLVmF1NnQ3YkpPblJZVmhCL05ZK2h4c3Q3VGZVeWErRkNGeDJDY0RFMCt6NnM1d1E4cVlxRUpKN3lCUVFheWxUaXlCVWdpb21pRThQbmRyU3E0MEpvQlVoYVo3a25SeDNnaGlZR3B6YTJFc3J0KytQRlpHSE1CakpzbGR2OXRPeTN6ZmNUWmI4N2xnUlZCRW1LeHlPWElHcmthb3NkSnJ0R2NqUlUwTWZseW1zVzgrcTg3WEpzajFpWGdUMFZQMGd0eGN6ZlFDUWFBbz0iLCJtYWMiOiI1MjUzMzQ0OGQ1NTE1NTBkNzI5ZjQwZWE4OTFiNDJiNTM2ZjBiYjg4NTkxNDBhZjI4MjEwZmIwMzg3NzQ2ZWNhIiwidGFnIjoiIn0%3D; SL_G_WPT_TO=fr; XSRF-TOKEN=eyJpdiI6IlBRN3h6NFZOeDVZajZGK0oyb2VMQVE9PSIsInZhbHVlIjoiQTQwdXBWQXVOU1Bqa3UyWGpLMDVzdThYVzVZSUVEd0F3OXNsRENXUzRuazZFR2x5d3ovc2VhYU5GUjBiUHNybjdOejRjM05iWlgwNHI2S2Vyb3AxZjRTMmpzNXRtOEp6OWlQQkRBaTF1TlJ1N1FzL3p3VmQrZDYzQzhHd3duTGUiLCJtYWMiOiJhNTk3ZDk1YTY5MjQ4OWNhYzY0ZDYyYmZhMTY0YjYxNGQ0MWU4NjhjMjE4MTNkZmVkNTAxMDViYzk1YzUwMzYyIiwidGFnIjoiIn0%3D; deaseapp_session=eyJpdiI6IjVrbFBmVFkwZ0VHR1hJYTJCUWxtN2c9PSIsInZhbHVlIjoiYjVWN2dabTlIYzE3VnJkSkJ4NXkxWmVGRWs4czV6bXBoMXgyODBMSVJjUGM4M0ZGZmhLbm9GR2Q4SzFCSnNmNzhUeEhNMGRXeFpQRitUd2ZhUVN2MEE0QjV1cVNRQmErajhzZUYxaUQ0UUhLUUlRY3d0YUNjOVNoa3I1dFhiSUwiLCJtYWMiOiJlMDA0MDQ4MDYwNjU1MjhhNWI2Y2ViZjEzNDY3ZjFhNTIxM2Y1MzQ5YWNjMzU3MWY2OWNiYzdhNTQ5YzM4NmFjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1185468861 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>SL_G_WPT_TO</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VLPBp8OZ5DPQQjor5cQrLq4YgrNA8oclmzN41QBj</span>\"\n  \"<span class=sf-dump-key>deaseapp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">U2ZZWlej1OcYr85cJVMgNe4TO1whPuiJSgYVa3MW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1185468861\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1424910780 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 04 Jun 2025 14:30:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">http://localhost/Admin-panel/login/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"414 characters\">XSRF-TOKEN=eyJpdiI6InBhRlF6WkhXL2JlNVdHK1hQOTAreXc9PSIsInZhbHVlIjoiNjBzU0tkU1hkVTJBeCttVGZZMVhkVHZFc2Eza1NuTjBIaUtRN0VGZ0NnSFhFZmNIYVlRZWUreG5QS01ranNuZmdVcGpWYzU4R1lsSFZaaWtWa0dZS1Z1ZEkrQjhQTUdQRVlzcUlYck41OWdZWEZnK1JpUS9ZS0NwZTNFM3JZRWciLCJtYWMiOiIyNzdlM2RkODk2YmFjM2IzNGFjMTE4MGZkZWNlNjk5NWY1MzlkMWMxMWViMTFlODU5MmVkNGM4NzdjMDgwNDE4IiwidGFnIjoiIn0%3D; expires=Wed, 04 Jun 2025 16:30:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"430 characters\">deaseapp_session=eyJpdiI6IjJrV2VXTkdPNEp4SlhUcEEzdUtYTlE9PSIsInZhbHVlIjoiVnQ3Q2lWS3dPWGk0eFpjVHdsdm5CUDhzaVhmU244eDZscGNNZUFEYTFqZ2MyN2p5TXVYWUc4QlBvajJiVmhMR0F1VUZRSmlxTWVKRHArUjgzVjlaNGpNNjY4enBNbnM3eGJCanF3SC9ibFdzclBMYVl0d2tmNkRqSENqMlFwRFMiLCJtYWMiOiJlODQzNmViMTEwNmI2MGQ4YWVkN2RjYTVmYjU3NjE2NzY5MWVmOWMwZTcxNTdkYzcwYzE0ODkyMWU3YjZkYTFlIiwidGFnIjoiIn0%3D; expires=Wed, 04 Jun 2025 16:30:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InBhRlF6WkhXL2JlNVdHK1hQOTAreXc9PSIsInZhbHVlIjoiNjBzU0tkU1hkVTJBeCttVGZZMVhkVHZFc2Eza1NuTjBIaUtRN0VGZ0NnSFhFZmNIYVlRZWUreG5QS01ranNuZmdVcGpWYzU4R1lsSFZaaWtWa0dZS1Z1ZEkrQjhQTUdQRVlzcUlYck41OWdZWEZnK1JpUS9ZS0NwZTNFM3JZRWciLCJtYWMiOiIyNzdlM2RkODk2YmFjM2IzNGFjMTE4MGZkZWNlNjk5NWY1MzlkMWMxMWViMTFlODU5MmVkNGM4NzdjMDgwNDE4IiwidGFnIjoiIn0%3D; expires=Wed, 04-Jun-2025 16:30:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">deaseapp_session=eyJpdiI6IjJrV2VXTkdPNEp4SlhUcEEzdUtYTlE9PSIsInZhbHVlIjoiVnQ3Q2lWS3dPWGk0eFpjVHdsdm5CUDhzaVhmU244eDZscGNNZUFEYTFqZ2MyN2p5TXVYWUc4QlBvajJiVmhMR0F1VUZRSmlxTWVKRHArUjgzVjlaNGpNNjY4enBNbnM3eGJCanF3SC9ibFdzclBMYVl0d2tmNkRqSENqMlFwRFMiLCJtYWMiOiJlODQzNmViMTEwNmI2MGQ4YWVkN2RjYTVmYjU3NjE2NzY5MWVmOWMwZTcxNTdkYzcwYzE0ODkyMWU3YjZkYTFlIiwidGFnIjoiIn0%3D; expires=Wed, 04-Jun-2025 16:30:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1424910780\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-439753609 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VLPBp8OZ5DPQQjor5cQrLq4YgrNA8oclmzN41QBj</span>\"\n  \"<span class=sf-dump-key>landing_site_direction</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ltr</span>\"\n  \"<span class=sf-dump-key>six_captcha</span>\" => \"<span class=sf-dump-str title=\"5 characters\">m6Ydd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/Admin-panel/logout</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>site_direction</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ltr</span>\"\n  \"<span class=sf-dump-key>dash_params</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>zone_id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">all</span>\"\n    \"<span class=sf-dump-key>module_id</span>\" => <span class=sf-dump-num>1</span>\n    \"<span class=sf-dump-key>statistics_type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">overall</span>\"\n    \"<span class=sf-dump-key>user_overview</span>\" => \"<span class=sf-dump-str title=\"7 characters\">overall</span>\"\n    \"<span class=sf-dump-key>commission_overview</span>\" => \"<span class=sf-dump-str title=\"9 characters\">this_year</span>\"\n    \"<span class=sf-dump-key>business_overview</span>\" => \"<span class=sf-dump-str title=\"7 characters\">overall</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-439753609\", {\"maxDepth\":0})</script>\n"}}