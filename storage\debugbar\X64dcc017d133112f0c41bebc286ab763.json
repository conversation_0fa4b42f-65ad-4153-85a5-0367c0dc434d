{"__meta": {"id": "X64dcc017d133112f0c41bebc286ab763", "datetime": "2025-06-04 15:30:27", "utime": 1749047427.748916, "method": "GET", "uri": "/Admin-panel/admin", "ip": "::1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749047409.75164, "end": 1749047427.748944, "duration": 17.99730396270752, "duration_str": "18s", "measures": [{"label": "Booting", "start": 1749047409.75164, "relative_start": 0, "end": **********.033952, "relative_end": **********.033952, "duration": 2.2823119163513184, "duration_str": "2.28s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.033969, "relative_start": 2.2823288440704346, "end": 1749047427.748947, "relative_end": 2.86102294921875e-06, "duration": 15.714977979660034, "duration_str": "15.71s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53844456, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 13, "templates": [{"name": "admin-views.dashboard-grocery", "param_count": null, "params": [], "start": **********.946691, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Admin-panel\\resources\\views/admin-views/dashboard-grocery.blade.phpadmin-views.dashboard-grocery", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Fadmin-views%2Fdashboard-grocery.blade.php&line=1", "ajax": false, "filename": "dashboard-grocery.blade.php", "line": "?"}}, {"name": "admin-views.partials._zone-change", "param_count": null, "params": [], "start": 1749047421.606587, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Admin-panel\\resources\\views/admin-views/partials/_zone-change.blade.phpadmin-views.partials._zone-change", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Fadmin-views%2Fpartials%2F_zone-change.blade.php&line=1", "ajax": false, "filename": "_zone-change.blade.php", "line": "?"}}, {"name": "admin-views.partials._top-restaurants", "param_count": null, "params": [], "start": 1749047421.654076, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Admin-panel\\resources\\views/admin-views/partials/_top-restaurants.blade.phpadmin-views.partials._top-restaurants", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Fadmin-views%2Fpartials%2F_top-restaurants.blade.php&line=1", "ajax": false, "filename": "_top-restaurants.blade.php", "line": "?"}}, {"name": "admin-views.partials._popular-restaurants", "param_count": null, "params": [], "start": 1749047421.951781, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Admin-panel\\resources\\views/admin-views/partials/_popular-restaurants.blade.phpadmin-views.partials._popular-restaurants", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Fadmin-views%2Fpartials%2F_popular-restaurants.blade.php&line=1", "ajax": false, "filename": "_popular-restaurants.blade.php", "line": "?"}}, {"name": "admin-views.partials._top-selling-foods", "param_count": null, "params": [], "start": 1749047421.996535, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Admin-panel\\resources\\views/admin-views/partials/_top-selling-foods.blade.phpadmin-views.partials._top-selling-foods", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Fadmin-views%2Fpartials%2F_top-selling-foods.blade.php&line=1", "ajax": false, "filename": "_top-selling-foods.blade.php", "line": "?"}}, {"name": "admin-views.partials._top-rated-foods", "param_count": null, "params": [], "start": 1749047422.16996, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Admin-panel\\resources\\views/admin-views/partials/_top-rated-foods.blade.phpadmin-views.partials._top-rated-foods", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Fadmin-views%2Fpartials%2F_top-rated-foods.blade.php&line=1", "ajax": false, "filename": "_top-rated-foods.blade.php", "line": "?"}}, {"name": "admin-views.partials._top-deliveryman", "param_count": null, "params": [], "start": 1749047422.324217, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Admin-panel\\resources\\views/admin-views/partials/_top-deliveryman.blade.phpadmin-views.partials._top-deliveryman", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Fadmin-views%2Fpartials%2F_top-deliveryman.blade.php&line=1", "ajax": false, "filename": "_top-deliveryman.blade.php", "line": "?"}}, {"name": "admin-views.partials._top-customer", "param_count": null, "params": [], "start": 1749047422.773398, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Admin-panel\\resources\\views/admin-views/partials/_top-customer.blade.phpadmin-views.partials._top-customer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Fadmin-views%2Fpartials%2F_top-customer.blade.php&line=1", "ajax": false, "filename": "_top-customer.blade.php", "line": "?"}}, {"name": "layouts.admin.app", "param_count": null, "params": [], "start": 1749047422.849476, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/app.blade.phplayouts.admin.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "layouts.admin.partials._front-settings", "param_count": null, "params": [], "start": 1749047423.084645, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_front-settings.blade.phplayouts.admin.partials._front-settings", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_front-settings.blade.php&line=1", "ajax": false, "filename": "_front-settings.blade.php", "line": "?"}}, {"name": "layouts.admin.partials._header", "param_count": null, "params": [], "start": 1749047423.089642, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_header.blade.phplayouts.admin.partials._header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_header.blade.php&line=1", "ajax": false, "filename": "_header.blade.php", "line": "?"}}, {"name": "layouts.admin.partials._sidebar_grocery", "param_count": null, "params": [], "start": 1749047424.389084, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.phplayouts.admin.partials._sidebar_grocery", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=1", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "?"}}, {"name": "layouts.admin.partials._footer", "param_count": null, "params": [], "start": 1749047426.869679, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_footer.blade.phplayouts.admin.partials._footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_footer.blade.php&line=1", "ajax": false, "filename": "_footer.blade.php", "line": "?"}}]}, "route": {"uri": "GET admin", "middleware": "web, admin, current-module", "controller": "App\\Http\\Controllers\\Admin\\DashboardController@dashboard", "as": "admin.dashboard", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=217\" onclick=\"\">app/Http/Controllers/Admin/DashboardController.php:217-239</a>"}, "queries": {"nb_statements": 110, "nb_failed_statements": 0, "accumulated_duration": 11.61983, "accumulated_duration_str": "11.62s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 10 queries only show the query. Limit can be raised in the config. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select * from `business_settings` where `key` = 'system_language' limit 1", "type": "query", "params": [], "bindings": ["system_language"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "middleware", "name": "localization", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\Localization.php", "line": 24}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 78}], "start": **********.078295, "duration": 0.*****************, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "localization:24", "source": "middleware::localization:24", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FMiddleware%2FLocalization.php&line=24", "ajax": false, "filename": "Localization.php", "line": "24"}, "connection": "dease_test", "start_percent": 0, "width_percent": 0.884}, {"sql": "select * from `admins` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": "middleware", "name": "admin", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\AdminMiddleware.php", "line": 21}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": "middleware", "name": "localization", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\Localization.php", "line": 60}], "start": **********.201443, "duration": 0.10219, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "dease_test", "start_percent": 0.884, "width_percent": 0.879}, {"sql": "select * from `modules` where `status` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "middleware", "name": "current-module", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\CurrentModule.php", "line": 31}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "admin", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\AdminMiddleware.php", "line": 25}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "localization", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\Localization.php", "line": 60}], "start": **********.311756, "duration": 0.10259, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "current-module:31", "source": "middleware::current-module:31", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FMiddleware%2FCurrentModule.php&line=31", "ajax": false, "filename": "CurrentModule.php", "line": "31"}, "connection": "dease_test", "start_percent": 1.763, "width_percent": 0.883}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (1, 2, 5, 7) and `translations`.`translationable_type` = 'App\\Models\\Module' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\Module", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "current-module", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\CurrentModule.php", "line": 31}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": "middleware", "name": "admin", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\AdminMiddleware.php", "line": 25}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": "middleware", "name": "localization", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\Localization.php", "line": 60}], "start": **********.424031, "duration": 0.10465, "duration_str": "105ms", "memory": 0, "memory_str": null, "filename": "current-module:31", "source": "middleware::current-module:31", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FMiddleware%2FCurrentModule.php&line=31", "ajax": false, "filename": "CurrentModule.php", "line": "31"}, "connection": "dease_test", "start_percent": 2.646, "width_percent": 0.901}, {"sql": "select * from `users`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 447}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.543905, "duration": 0.11118, "duration_str": "111ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:447", "source": "app/Http/Controllers/Admin/DashboardController.php:447", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=447", "ajax": false, "filename": "DashboardController.php", "line": "447"}, "connection": "dease_test", "start_percent": 3.547, "width_percent": 0.957}, {"sql": "select count(*) as aggregate from `orders` where `delivery_man_id` is null and `order_type` in ('delivery', 'parcel') and `order_status` not in ('delivered', 'failed', 'canceled', 'refund_requested', 'refund_request_canceled', 'refunded') and `module_id` = 1 and (`order_type` = 'take_away' or `order_type` = 'delivery') and ((created_at <> schedule_at and (`schedule_at` between '2025-06-04 15:30:12' and '2025-06-04 16:00:12') or `schedule_at` < '2025-06-04 15:30:12') or created_at = schedule_at)", "type": "query", "params": [], "bindings": ["delivery", "parcel", "delivered", "failed", "canceled", "refund_requested", "refund_request_canceled", "refunded", "1", "take_away", "delivery", "2025-06-04 15:30:12", "2025-06-04 16:00:12", "2025-06-04 15:30:12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 485}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.665072, "duration": 0.10434, "duration_str": "104ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:485", "source": "app/Http/Controllers/Admin/DashboardController.php:485", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=485", "ajax": false, "filename": "DashboardController.php", "line": "485"}, "connection": "dease_test", "start_percent": 4.503, "width_percent": 0.898}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'accepted' and `module_id` = 1 and (`order_type` = 'take_away' or `order_type` = 'delivery')", "type": "query", "params": [], "bindings": ["accepted", "1", "take_away", "delivery"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 486}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.775379, "duration": 0.10121, "duration_str": "101ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:486", "source": "app/Http/Controllers/Admin/DashboardController.php:486", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=486", "ajax": false, "filename": "DashboardController.php", "line": "486"}, "connection": "dease_test", "start_percent": 5.401, "width_percent": 0.871}, {"sql": "select count(*) as aggregate from `orders` where `order_status` in ('confirmed', 'processing', 'handover') and `module_id` = 1 and (`order_type` = 'take_away' or `order_type` = 'delivery')", "type": "query", "params": [], "bindings": ["confirmed", "processing", "handover", "1", "take_away", "delivery"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 487}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.882747, "duration": 0.10171, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:487", "source": "app/Http/Controllers/Admin/DashboardController.php:487", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=487", "ajax": false, "filename": "DashboardController.php", "line": "487"}, "connection": "dease_test", "start_percent": 6.272, "width_percent": 0.875}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'picked_up' and `module_id` = 1 and (`order_type` = 'take_away' or `order_type` = 'delivery')", "type": "query", "params": [], "bindings": ["picked_up", "1", "take_away", "delivery"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 488}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.9904132, "duration": 0.10018, "duration_str": "100ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:488", "source": "app/Http/Controllers/Admin/DashboardController.php:488", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=488", "ajax": false, "filename": "DashboardController.php", "line": "488"}, "connection": "dease_test", "start_percent": 7.148, "width_percent": 0.862}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'delivered' and `module_id` = 1 and (`order_type` = 'take_away' or `order_type` = 'delivery')", "type": "query", "params": [], "bindings": ["delivered", "1", "take_away", "delivery"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 489}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0967891, "duration": 0.10337, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:489", "source": "app/Http/Controllers/Admin/DashboardController.php:489", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=489", "ajax": false, "filename": "DashboardController.php", "line": "489"}, "connection": "dease_test", "start_percent": 8.01, "width_percent": 0.89}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'canceled' and `module_id` = 1 and (`order_type` = 'take_away' or `order_type` = 'delivery')", "type": "query", "params": [], "bindings": ["canceled", "1", "take_away", "delivery"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 490}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2060611, "duration": 0.10142, "duration_str": "101ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:490", "source": "app/Http/Controllers/Admin/DashboardController.php:490", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=490", "ajax": false, "filename": "DashboardController.php", "line": "490"}, "connection": "dease_test", "start_percent": 8.899, "width_percent": 0.873}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'failed' and `module_id` = 1 and (`order_type` = 'take_away' or `order_type` = 'delivery')", "type": "query", "params": [], "bindings": ["failed", "1", "take_away", "delivery"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 491}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.313896, "duration": 0.10129, "duration_str": "101ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:491", "source": "app/Http/Controllers/Admin/DashboardController.php:491", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=491", "ajax": false, "filename": "DashboardController.php", "line": "491"}, "connection": "dease_test", "start_percent": 9.772, "width_percent": 0.872}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'refunded' and `module_id` = 1 and (`order_type` = 'take_away' or `order_type` = 'delivery')", "type": "query", "params": [], "bindings": ["refunded", "1", "take_away", "delivery"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 492}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.421076, "duration": 0.1044, "duration_str": "104ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:492", "source": "app/Http/Controllers/Admin/DashboardController.php:492", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=492", "ajax": false, "filename": "DashboardController.php", "line": "492"}, "connection": "dease_test", "start_percent": 10.644, "width_percent": 0.898}, {"sql": "select count(*) as aggregate from `orders` where `module_id` = 1 and (`order_type` = 'take_away' or `order_type` = 'delivery')", "type": "query", "params": [], "bindings": ["1", "take_away", "delivery"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 493}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.531429, "duration": 0.10373, "duration_str": "104ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:493", "source": "app/Http/Controllers/Admin/DashboardController.php:493", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=493", "ajax": false, "filename": "DashboardController.php", "line": "493"}, "connection": "dease_test", "start_percent": 11.542, "width_percent": 0.893}, {"sql": "select count(*) as aggregate from `items` where `module_id` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 494}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.640891, "duration": 0.*****************, "duration_str": "99.32ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:494", "source": "app/Http/Controllers/Admin/DashboardController.php:494", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=494", "ajax": false, "filename": "DashboardController.php", "line": "494"}, "connection": "dease_test", "start_percent": 12.435, "width_percent": 0.855}, {"sql": "select count(*) as aggregate from `stores` where `module_id` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 495}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.746248, "duration": 0.10229, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:495", "source": "app/Http/Controllers/Admin/DashboardController.php:495", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=495", "ajax": false, "filename": "DashboardController.php", "line": "495"}, "connection": "dease_test", "start_percent": 13.29, "width_percent": 0.88}, {"sql": "select count(*) as aggregate from `orders` where `module_id` = 1 and date(`schedule_at`) >= '2025-05-05' and (`order_type` = 'take_away' or `order_type` = 'delivery')", "type": "query", "params": [], "bindings": ["1", "2025-05-05", "take_away", "delivery"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 497}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.854447, "duration": 0.10067, "duration_str": "101ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:497", "source": "app/Http/Controllers/Admin/DashboardController.php:497", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=497", "ajax": false, "filename": "DashboardController.php", "line": "497"}, "connection": "dease_test", "start_percent": 14.17, "width_percent": 0.866}, {"sql": "select count(*) as aggregate from `items` where `module_id` = 1 and date(`created_at`) >= '2025-05-05'", "type": "query", "params": [], "bindings": ["1", "2025-05-05"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 498}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.960971, "duration": 0.10139, "duration_str": "101ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:498", "source": "app/Http/Controllers/Admin/DashboardController.php:498", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=498", "ajax": false, "filename": "DashboardController.php", "line": "498"}, "connection": "dease_test", "start_percent": 15.037, "width_percent": 0.873}, {"sql": "select count(*) as aggregate from `stores` where `module_id` = 1 and date(`created_at`) >= '2025-05-05'", "type": "query", "params": [], "bindings": ["1", "2025-05-05"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 499}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.068085, "duration": 0.09925, "duration_str": "99.25ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:499", "source": "app/Http/Controllers/Admin/DashboardController.php:499", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=499", "ajax": false, "filename": "DashboardController.php", "line": "499"}, "connection": "dease_test", "start_percent": 15.909, "width_percent": 0.854}, {"sql": "select count(*) as aggregate from `users` where date(`created_at`) >= '2025-05-05'", "type": "query", "params": [], "bindings": ["2025-05-05"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 500}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.173032, "duration": 0.10422, "duration_str": "104ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:500", "source": "app/Http/Controllers/Admin/DashboardController.php:500", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=500", "ajax": false, "filename": "DashboardController.php", "line": "500"}, "connection": "dease_test", "start_percent": 16.763, "width_percent": 0.897}, {"sql": "select count(*) as aggregate from `users` where `id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 593}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 629}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2990768, "duration": 0.10033, "duration_str": "100ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:593", "source": "app/Http/Controllers/Admin/DashboardController.php:593", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=593", "ajax": false, "filename": "DashboardController.php", "line": "593"}, "connection": "dease_test", "start_percent": 17.66, "width_percent": 0.863}, {"sql": "select count(*) as aggregate from `stores` where `module_id` = 1 and `id` is not null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 594}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 629}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.40508, "duration": 0.10127, "duration_str": "101ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:594", "source": "app/Http/Controllers/Admin/DashboardController.php:594", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=594", "ajax": false, "filename": "DashboardController.php", "line": "594"}, "connection": "dease_test", "start_percent": 18.524, "width_percent": 0.872}, {"sql": "select count(*) as aggregate from `delivery_men` where `application_status` = 'approved' and `type` = 'zone_wise'", "type": "query", "params": [], "bindings": ["approved", "zone_wise"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 595}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 629}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5121949, "duration": 0.10012, "duration_str": "100ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:595", "source": "app/Http/Controllers/Admin/DashboardController.php:595", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=595", "ajax": false, "filename": "DashboardController.php", "line": "595"}, "connection": "dease_test", "start_percent": 19.395, "width_percent": 0.862}, {"sql": "select `store_id`, COUNT(store_id) as count from `wishlists` where exists (select * from `stores` where `wishlists`.`store_id` = `stores`.`id`) and exists (select * from `stores` where `wishlists`.`store_id` = `stores`.`id` and `module_id` = 1) group by `store_id` order by `count` desc limit 6", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 643}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.632777, "duration": 0.10145, "duration_str": "101ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:643", "source": "app/Http/Controllers/Admin/DashboardController.php:643", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=643", "ajax": false, "filename": "DashboardController.php", "line": "643"}, "connection": "dease_test", "start_percent": 20.257, "width_percent": 0.873}, {"sql": "select * from `items` where exists (select * from `stores` where `items`.`store_id` = `stores`.`id` and `module_id` = 1) order by `order_count` desc limit 6", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 657}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.740442, "duration": 0.12228, "duration_str": "122ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:657", "source": "app/Http/Controllers/Admin/DashboardController.php:657", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=657", "ajax": false, "filename": "DashboardController.php", "line": "657"}, "connection": "dease_test", "start_percent": 21.13, "width_percent": 1.052}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (4, 5) and `translations`.`translationable_type` = 'App\\Models\\Item' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\Item", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 657}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.8705912, "duration": 0.10177, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:657", "source": "app/Http/Controllers/Admin/DashboardController.php:657", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=657", "ajax": false, "filename": "DashboardController.php", "line": "657"}, "connection": "dease_test", "start_percent": 22.182, "width_percent": 0.876}, {"sql": "select * from `items` where exists (select * from `stores` where `items`.`store_id` = `stores`.`id` and `module_id` = 1) order by `rating_count` desc limit 6", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 671}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.979244, "duration": 0.10527, "duration_str": "105ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:671", "source": "app/Http/Controllers/Admin/DashboardController.php:671", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=671", "ajax": false, "filename": "DashboardController.php", "line": "671"}, "connection": "dease_test", "start_percent": 23.058, "width_percent": 0.906}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (4, 5) and `translations`.`translationable_type` = 'App\\Models\\Item' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\Item", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 671}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.090631, "duration": 0.38565, "duration_str": "386ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:671", "source": "app/Http/Controllers/Admin/DashboardController.php:671", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=671", "ajax": false, "filename": "DashboardController.php", "line": "671"}, "connection": "dease_test", "start_percent": 23.964, "width_percent": 3.319}, {"sql": "select `delivery_men`.*, (select count(*) from `orders` where `delivery_men`.`id` = `orders`.`delivery_man_id`) as `orders_count` from `delivery_men` where `type` = 'zone_wise' order by `orders_count` desc limit 6", "type": "query", "params": [], "bindings": ["zone_wise"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 679}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.491877, "duration": 0.10386, "duration_str": "104ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:679", "source": "app/Http/Controllers/Admin/DashboardController.php:679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=679", "ajax": false, "filename": "DashboardController.php", "line": "679"}, "connection": "dease_test", "start_percent": 27.283, "width_percent": 0.894}, {"sql": "select * from `users` order by `order_count` desc limit 6", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 686}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.6021612, "duration": 0.10369, "duration_str": "104ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:686", "source": "app/Http/Controllers/Admin/DashboardController.php:686", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=686", "ajax": false, "filename": "DashboardController.php", "line": "686"}, "connection": "dease_test", "start_percent": 28.177, "width_percent": 0.892}, {"sql": "select * from `stores` where `module_id` = 1 order by `order_count` desc limit 6", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 696}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.711785, "duration": 0.11186, "duration_str": "112ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:696", "source": "app/Http/Controllers/Admin/DashboardController.php:696", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=696", "ajax": false, "filename": "DashboardController.php", "line": "696"}, "connection": "dease_test", "start_percent": 29.069, "width_percent": 0.963}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (6, 7, 21, 23) and `translations`.`translationable_type` = 'App\\Models\\Store' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\Store", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 696}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.831043, "duration": 0.10164, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:696", "source": "app/Http/Controllers/Admin/DashboardController.php:696", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=696", "ajax": false, "filename": "DashboardController.php", "line": "696"}, "connection": "dease_test", "start_percent": 30.032, "width_percent": 0.875}, {"sql": "select sum(`order_amount`) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '01' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "01", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 744}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.022454, "duration": 0.10163, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:744", "source": "app/Http/Controllers/Admin/DashboardController.php:744", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=744", "ajax": false, "filename": "DashboardController.php", "line": "744"}, "connection": "dease_test", "start_percent": 30.906, "width_percent": 0.875}, {"sql": "select sum(admin_commission + admin_expense - delivery_fee_comission) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '01' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "01", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 753}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.130155, "duration": 0.10016, "duration_str": "100ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:753", "source": "app/Http/Controllers/Admin/DashboardController.php:753", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=753", "ajax": false, "filename": "DashboardController.php", "line": "753"}, "connection": "dease_test", "start_percent": 31.781, "width_percent": 0.862}, {"sql": "select sum(`delivery_fee_comission`) as aggregate from `order_transactions` where `module_id` = 1 and month(`created_at`) = '01' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["1", "01", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 761}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.236216, "duration": 0.10026, "duration_str": "100ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:761", "source": "app/Http/Controllers/Admin/DashboardController.php:761", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=761", "ajax": false, "filename": "DashboardController.php", "line": "761"}, "connection": "dease_test", "start_percent": 32.643, "width_percent": 0.863}, {"sql": "select sum(`order_amount`) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '02' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "02", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 744}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.342748, "duration": 0.0994, "duration_str": "99.4ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:744", "source": "app/Http/Controllers/Admin/DashboardController.php:744", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=744", "ajax": false, "filename": "DashboardController.php", "line": "744"}, "connection": "dease_test", "start_percent": 33.506, "width_percent": 0.855}, {"sql": "select sum(admin_commission + admin_expense - delivery_fee_comission) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '02' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "02", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 753}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.44816, "duration": 0.09998, "duration_str": "99.98ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:753", "source": "app/Http/Controllers/Admin/DashboardController.php:753", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=753", "ajax": false, "filename": "DashboardController.php", "line": "753"}, "connection": "dease_test", "start_percent": 34.361, "width_percent": 0.86}, {"sql": "select sum(`delivery_fee_comission`) as aggregate from `order_transactions` where `module_id` = 1 and month(`created_at`) = '02' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["1", "02", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 761}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.554058, "duration": 0.10352, "duration_str": "104ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:761", "source": "app/Http/Controllers/Admin/DashboardController.php:761", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=761", "ajax": false, "filename": "DashboardController.php", "line": "761"}, "connection": "dease_test", "start_percent": 35.222, "width_percent": 0.891}, {"sql": "select sum(`order_amount`) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '03' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "03", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 744}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.6636531, "duration": 0.10196, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:744", "source": "app/Http/Controllers/Admin/DashboardController.php:744", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=744", "ajax": false, "filename": "DashboardController.php", "line": "744"}, "connection": "dease_test", "start_percent": 36.113, "width_percent": 0.877}, {"sql": "select sum(admin_commission + admin_expense - delivery_fee_comission) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '03' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "03", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 753}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.771755, "duration": 0.10313, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:753", "source": "app/Http/Controllers/Admin/DashboardController.php:753", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=753", "ajax": false, "filename": "DashboardController.php", "line": "753"}, "connection": "dease_test", "start_percent": 36.99, "width_percent": 0.888}, {"sql": "select sum(`delivery_fee_comission`) as aggregate from `order_transactions` where `module_id` = 1 and month(`created_at`) = '03' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["1", "03", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 761}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.8807259, "duration": 0.10098, "duration_str": "101ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:761", "source": "app/Http/Controllers/Admin/DashboardController.php:761", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=761", "ajax": false, "filename": "DashboardController.php", "line": "761"}, "connection": "dease_test", "start_percent": 37.878, "width_percent": 0.869}, {"sql": "select sum(`order_amount`) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '04' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "04", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 744}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.9877942, "duration": 0.10052, "duration_str": "101ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:744", "source": "app/Http/Controllers/Admin/DashboardController.php:744", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=744", "ajax": false, "filename": "DashboardController.php", "line": "744"}, "connection": "dease_test", "start_percent": 38.747, "width_percent": 0.865}, {"sql": "select sum(admin_commission + admin_expense - delivery_fee_comission) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '04' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "04", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 753}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.09482, "duration": 0.*****************, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:753", "source": "app/Http/Controllers/Admin/DashboardController.php:753", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=753", "ajax": false, "filename": "DashboardController.php", "line": "753"}, "connection": "dease_test", "start_percent": 39.612, "width_percent": 0.886}, {"sql": "select sum(`delivery_fee_comission`) as aggregate from `order_transactions` where `module_id` = 1 and month(`created_at`) = '04' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["1", "04", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 761}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.205754, "duration": 0.11083, "duration_str": "111ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:761", "source": "app/Http/Controllers/Admin/DashboardController.php:761", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=761", "ajax": false, "filename": "DashboardController.php", "line": "761"}, "connection": "dease_test", "start_percent": 40.498, "width_percent": 0.954}, {"sql": "select sum(`order_amount`) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '05' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "05", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 744}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.323007, "duration": 0.*****************, "duration_str": "104ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:744", "source": "app/Http/Controllers/Admin/DashboardController.php:744", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=744", "ajax": false, "filename": "DashboardController.php", "line": "744"}, "connection": "dease_test", "start_percent": 41.452, "width_percent": 0.891}, {"sql": "select sum(admin_commission + admin_expense - delivery_fee_comission) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '05' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "05", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 753}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.43261, "duration": 0.10253, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:753", "source": "app/Http/Controllers/Admin/DashboardController.php:753", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=753", "ajax": false, "filename": "DashboardController.php", "line": "753"}, "connection": "dease_test", "start_percent": 42.343, "width_percent": 0.882}, {"sql": "select sum(`delivery_fee_comission`) as aggregate from `order_transactions` where `module_id` = 1 and month(`created_at`) = '05' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["1", "05", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 761}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.541235, "duration": 0.10258, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:761", "source": "app/Http/Controllers/Admin/DashboardController.php:761", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=761", "ajax": false, "filename": "DashboardController.php", "line": "761"}, "connection": "dease_test", "start_percent": 43.225, "width_percent": 0.883}, {"sql": "select sum(`order_amount`) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '06' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "06", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 744}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.6498759, "duration": 0.10116, "duration_str": "101ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:744", "source": "app/Http/Controllers/Admin/DashboardController.php:744", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=744", "ajax": false, "filename": "DashboardController.php", "line": "744"}, "connection": "dease_test", "start_percent": 44.108, "width_percent": 0.871}, {"sql": "select sum(admin_commission + admin_expense - delivery_fee_comission) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '06' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "06", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 753}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.7579582, "duration": 0.10192, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:753", "source": "app/Http/Controllers/Admin/DashboardController.php:753", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=753", "ajax": false, "filename": "DashboardController.php", "line": "753"}, "connection": "dease_test", "start_percent": 44.978, "width_percent": 0.877}, {"sql": "select sum(`delivery_fee_comission`) as aggregate from `order_transactions` where `module_id` = 1 and month(`created_at`) = '06' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["1", "06", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 761}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.865812, "duration": 0.10004, "duration_str": "100ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:761", "source": "app/Http/Controllers/Admin/DashboardController.php:761", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=761", "ajax": false, "filename": "DashboardController.php", "line": "761"}, "connection": "dease_test", "start_percent": 45.856, "width_percent": 0.861}, {"sql": "select sum(`order_amount`) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '07' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "07", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 744}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.9722621, "duration": 0.10332, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:744", "source": "app/Http/Controllers/Admin/DashboardController.php:744", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=744", "ajax": false, "filename": "DashboardController.php", "line": "744"}, "connection": "dease_test", "start_percent": 46.717, "width_percent": 0.889}, {"sql": "select sum(admin_commission + admin_expense - delivery_fee_comission) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '07' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "07", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 753}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.081684, "duration": 0.10211, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:753", "source": "app/Http/Controllers/Admin/DashboardController.php:753", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=753", "ajax": false, "filename": "DashboardController.php", "line": "753"}, "connection": "dease_test", "start_percent": 47.606, "width_percent": 0.879}, {"sql": "select sum(`delivery_fee_comission`) as aggregate from `order_transactions` where `module_id` = 1 and month(`created_at`) = '07' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["1", "07", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 761}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.189814, "duration": 0.1002, "duration_str": "100ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:761", "source": "app/Http/Controllers/Admin/DashboardController.php:761", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=761", "ajax": false, "filename": "DashboardController.php", "line": "761"}, "connection": "dease_test", "start_percent": 48.484, "width_percent": 0.862}, {"sql": "select sum(`order_amount`) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '08' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "08", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 744}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.296036, "duration": 0.10166, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:744", "source": "app/Http/Controllers/Admin/DashboardController.php:744", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=744", "ajax": false, "filename": "DashboardController.php", "line": "744"}, "connection": "dease_test", "start_percent": 49.347, "width_percent": 0.875}, {"sql": "select sum(admin_commission + admin_expense - delivery_fee_comission) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '08' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "08", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 753}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.4038231, "duration": 0.10092, "duration_str": "101ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:753", "source": "app/Http/Controllers/Admin/DashboardController.php:753", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=753", "ajax": false, "filename": "DashboardController.php", "line": "753"}, "connection": "dease_test", "start_percent": 50.222, "width_percent": 0.869}, {"sql": "select sum(`delivery_fee_comission`) as aggregate from `order_transactions` where `module_id` = 1 and month(`created_at`) = '08' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["1", "08", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 761}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.511289, "duration": 0.*****************, "duration_str": "105ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:761", "source": "app/Http/Controllers/Admin/DashboardController.php:761", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=761", "ajax": false, "filename": "DashboardController.php", "line": "761"}, "connection": "dease_test", "start_percent": 51.09, "width_percent": 0.901}, {"sql": "select sum(`order_amount`) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '09' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "09", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 744}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.622077, "duration": 0.*****************, "duration_str": "101ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:744", "source": "app/Http/Controllers/Admin/DashboardController.php:744", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=744", "ajax": false, "filename": "DashboardController.php", "line": "744"}, "connection": "dease_test", "start_percent": 51.991, "width_percent": 0.866}, {"sql": "select sum(admin_commission + admin_expense - delivery_fee_comission) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '09' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "09", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 753}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.728782, "duration": 0.10273, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:753", "source": "app/Http/Controllers/Admin/DashboardController.php:753", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=753", "ajax": false, "filename": "DashboardController.php", "line": "753"}, "connection": "dease_test", "start_percent": 52.857, "width_percent": 0.884}, {"sql": "select sum(`delivery_fee_comission`) as aggregate from `order_transactions` where `module_id` = 1 and month(`created_at`) = '09' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["1", "09", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 761}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.8369331, "duration": 0.10174, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:761", "source": "app/Http/Controllers/Admin/DashboardController.php:761", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=761", "ajax": false, "filename": "DashboardController.php", "line": "761"}, "connection": "dease_test", "start_percent": 53.741, "width_percent": 0.876}, {"sql": "select sum(`order_amount`) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '10' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "10", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 744}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.944707, "duration": 0.10221, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:744", "source": "app/Http/Controllers/Admin/DashboardController.php:744", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=744", "ajax": false, "filename": "DashboardController.php", "line": "744"}, "connection": "dease_test", "start_percent": 54.616, "width_percent": 0.88}, {"sql": "select sum(admin_commission + admin_expense - delivery_fee_comission) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '10' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "10", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 753}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.0530422, "duration": 0.10371, "duration_str": "104ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:753", "source": "app/Http/Controllers/Admin/DashboardController.php:753", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=753", "ajax": false, "filename": "DashboardController.php", "line": "753"}, "connection": "dease_test", "start_percent": 55.496, "width_percent": 0.893}, {"sql": "select sum(`delivery_fee_comission`) as aggregate from `order_transactions` where `module_id` = 1 and month(`created_at`) = '10' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["1", "10", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 761}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.162629, "duration": 0.10218, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:761", "source": "app/Http/Controllers/Admin/DashboardController.php:761", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=761", "ajax": false, "filename": "DashboardController.php", "line": "761"}, "connection": "dease_test", "start_percent": 56.388, "width_percent": 0.879}, {"sql": "select sum(`order_amount`) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '11' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "11", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 744}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.270973, "duration": 0.10513, "duration_str": "105ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:744", "source": "app/Http/Controllers/Admin/DashboardController.php:744", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=744", "ajax": false, "filename": "DashboardController.php", "line": "744"}, "connection": "dease_test", "start_percent": 57.268, "width_percent": 0.905}, {"sql": "select sum(admin_commission + admin_expense - delivery_fee_comission) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '11' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "11", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 753}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.384922, "duration": 0.10465, "duration_str": "105ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:753", "source": "app/Http/Controllers/Admin/DashboardController.php:753", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=753", "ajax": false, "filename": "DashboardController.php", "line": "753"}, "connection": "dease_test", "start_percent": 58.173, "width_percent": 0.901}, {"sql": "select sum(`delivery_fee_comission`) as aggregate from `order_transactions` where `module_id` = 1 and month(`created_at`) = '11' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["1", "11", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 761}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.4954531, "duration": 0.10355, "duration_str": "104ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:761", "source": "app/Http/Controllers/Admin/DashboardController.php:761", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=761", "ajax": false, "filename": "DashboardController.php", "line": "761"}, "connection": "dease_test", "start_percent": 59.073, "width_percent": 0.891}, {"sql": "select sum(`order_amount`) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '12' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "12", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 744}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.605186, "duration": 0.10247, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:744", "source": "app/Http/Controllers/Admin/DashboardController.php:744", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=744", "ajax": false, "filename": "DashboardController.php", "line": "744"}, "connection": "dease_test", "start_percent": 59.964, "width_percent": 0.882}, {"sql": "select sum(admin_commission + admin_expense - delivery_fee_comission) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '12' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "12", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 753}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.71375, "duration": 0.10096, "duration_str": "101ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:753", "source": "app/Http/Controllers/Admin/DashboardController.php:753", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=753", "ajax": false, "filename": "DashboardController.php", "line": "753"}, "connection": "dease_test", "start_percent": 60.846, "width_percent": 0.869}, {"sql": "select sum(`delivery_fee_comission`) as aggregate from `order_transactions` where `module_id` = 1 and month(`created_at`) = '12' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["1", "12", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 761}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.82091, "duration": 0.10575, "duration_str": "106ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:761", "source": "app/Http/Controllers/Admin/DashboardController.php:761", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=761", "ajax": false, "filename": "DashboardController.php", "line": "761"}, "connection": "dease_test", "start_percent": 61.715, "width_percent": 0.91}, {"sql": "select * from `business_settings` where (`key` = 'business_name') limit 1", "type": "query", "params": [], "bindings": ["business_name"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "admin-views.dashboard-grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/admin-views/dashboard-grocery.blade.php", "line": 1}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.962008, "duration": 0.10259, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "admin-views.dashboard-grocery:1", "source": "view::admin-views.dashboard-grocery:1", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Fadmin-views%2Fdashboard-grocery.blade.php&line=1", "ajax": false, "filename": "dashboard-grocery.blade.php", "line": "1"}, "connection": "dease_test", "start_percent": 62.625, "width_percent": 0.883}, {"sql": "select * from `modules` where `modules`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "admin-views.dashboard-grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/admin-views/dashboard-grocery.blade.php", "line": 10}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047420.072754, "duration": 0.10225, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "admin-views.dashboard-grocery:10", "source": "view::admin-views.dashboard-grocery:10", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Fadmin-views%2Fdashboard-grocery.blade.php&line=10", "ajax": false, "filename": "dashboard-grocery.blade.php", "line": "10"}, "connection": "dease_test", "start_percent": 63.508, "width_percent": 0.88}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (1) and `translations`.`translationable_type` = 'App\\Models\\Module' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\Module", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": "view", "name": "admin-views.dashboard-grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/admin-views/dashboard-grocery.blade.php", "line": 10}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047420.180161, "duration": 0.10267, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "admin-views.dashboard-grocery:10", "source": "view::admin-views.dashboard-grocery:10", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Fadmin-views%2Fdashboard-grocery.blade.php&line=10", "ajax": false, "filename": "dashboard-grocery.blade.php", "line": "10"}, "connection": "dease_test", "start_percent": 64.388, "width_percent": 0.884}, {"sql": "select * from `zones` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "admin-views.dashboard-grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/admin-views/dashboard-grocery.blade.php", "line": 27}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047420.9567, "duration": 0.10456, "duration_str": "105ms", "memory": 0, "memory_str": null, "filename": "admin-views.dashboard-grocery:27", "source": "view::admin-views.dashboard-grocery:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Fadmin-views%2Fdashboard-grocery.blade.php&line=27", "ajax": false, "filename": "dashboard-grocery.blade.php", "line": "27"}, "connection": "dease_test", "start_percent": 65.272, "width_percent": 0.9}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (1, 2) and `translations`.`translationable_type` = 'App\\Models\\Zone' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\Zone", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "admin-views.dashboard-grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/admin-views/dashboard-grocery.blade.php", "line": 27}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047421.067347, "duration": 0.10005, "duration_str": "100ms", "memory": 0, "memory_str": null, "filename": "admin-views.dashboard-grocery:27", "source": "view::admin-views.dashboard-grocery:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Fadmin-views%2Fdashboard-grocery.blade.php&line=27", "ajax": false, "filename": "dashboard-grocery.blade.php", "line": "27"}, "connection": "dease_test", "start_percent": 66.171, "width_percent": 0.861}, {"sql": "select * from `business_settings` where (`key` = 'currency_symbol_position') limit 1", "type": "query", "params": [], "bindings": ["currency_symbol_position"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\CentralLogics\\helpers.php", "line": 989}, {"index": 17, "namespace": "view", "name": "admin-views.dashboard-grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/admin-views/dashboard-grocery.blade.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749047421.250139, "duration": 0.10429000000000001, "duration_str": "104ms", "memory": 0, "memory_str": null, "filename": "helpers.php:989", "source": "app/CentralLogics/helpers.php:989", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FCentralLogics%2Fhelpers.php&line=989", "ajax": false, "filename": "helpers.php", "line": "989"}, "connection": "dease_test", "start_percent": 67.032, "width_percent": 0.898}, {"sql": "select * from `business_settings` where (`key` = 'currency') limit 1", "type": "query", "params": [], "bindings": ["currency"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\CentralLogics\\helpers.php", "line": 963}, {"index": 17, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\CentralLogics\\helpers.php", "line": 976}, {"index": 18, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\CentralLogics\\helpers.php", "line": 995}, {"index": 19, "namespace": "view", "name": "admin-views.dashboard-grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/admin-views/dashboard-grocery.blade.php", "line": 228}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1749047421.36013, "duration": 0.10233, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "helpers.php:963", "source": "app/CentralLogics/helpers.php:963", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FCentralLogics%2Fhelpers.php&line=963", "ajax": false, "filename": "helpers.php", "line": "963"}, "connection": "dease_test", "start_percent": 67.93, "width_percent": 0.881}, {"sql": "select * from `currencies` where (`currency_code` = 'DZD') limit 1", "type": "query", "params": [], "bindings": ["DZD"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\CentralLogics\\helpers.php", "line": 976}, {"index": 17, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\CentralLogics\\helpers.php", "line": 995}, {"index": 18, "namespace": "view", "name": "admin-views.dashboard-grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/admin-views/dashboard-grocery.blade.php", "line": 228}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1749047421.468381, "duration": 0.10272, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "helpers.php:976", "source": "app/CentralLogics/helpers.php:976", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FCentralLogics%2Fhelpers.php&line=976", "ajax": false, "filename": "helpers.php", "line": "976"}, "connection": "dease_test", "start_percent": 68.811, "width_percent": 0.884}, {"sql": "select * from `business_settings` where `key` = 'country' limit 1", "type": "query", "params": [], "bindings": ["country"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.app", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/app.blade.php", "line": 4}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047422.86311, "duration": 0.10407, "duration_str": "104ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.app:4", "source": "view::layouts.admin.app:4", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fapp.blade.php&line=4", "ajax": false, "filename": "app.blade.php", "line": "4"}, "connection": "dease_test", "start_percent": 69.695, "width_percent": 0.896}, {"sql": "select * from `business_settings` where (`key` = 'icon') limit 1", "type": "query", "params": [], "bindings": ["icon"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.app", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/app.blade.php", "line": 15}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047422.9747522, "duration": 0.10158, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.app:15", "source": "view::layouts.admin.app:15", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fapp.blade.php&line=15", "ajax": false, "filename": "app.blade.php", "line": "15"}, "connection": "dease_test", "start_percent": 70.59, "width_percent": 0.874}, {"sql": "select * from `admin_roles` where `admin_roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\CentralLogics\\helpers.php", "line": 2196}, {"index": 22, "namespace": "view", "name": "layouts.admin.partials._header", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_header.blade.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749047423.1215591, "duration": 0.10158, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "helpers.php:2196", "source": "app/CentralLogics/helpers.php:2196", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FCentralLogics%2Fhelpers.php&line=2196", "ajax": false, "filename": "helpers.php", "line": "2196"}, "connection": "dease_test", "start_percent": 71.464, "width_percent": 0.874}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (1) and `translations`.`translationable_type` = 'App\\Models\\AdminRole' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\AdminRole", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\CentralLogics\\helpers.php", "line": 2196}, {"index": 27, "namespace": "view", "name": "layouts.admin.partials._header", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_header.blade.php", "line": 57}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749047423.228151, "duration": 0.10019, "duration_str": "100ms", "memory": 0, "memory_str": null, "filename": "helpers.php:2196", "source": "app/CentralLogics/helpers.php:2196", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FCentralLogics%2Fhelpers.php&line=2196", "ajax": false, "filename": "helpers.php", "line": "2196"}, "connection": "dease_test", "start_percent": 72.339, "width_percent": 0.862}, {"sql": "select count(*) as aggregate from `conversations` where (`sender_type` = 'admin' or `receiver_type` = 'admin') and `unread_message_count` > '0'", "type": "query", "params": [], "bindings": ["admin", "admin", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._header", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_header.blade.php", "line": 115}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047423.362256, "duration": 0.10217, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._header:115", "source": "view::layouts.admin.partials._header:115", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_header.blade.php&line=115", "ajax": false, "filename": "_header.blade.php", "line": "115"}, "connection": "dease_test", "start_percent": 73.201, "width_percent": 0.879}, {"sql": "select * from `business_settings` where `key` = 'system_language' limit 1", "type": "query", "params": [], "bindings": ["system_language"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._header", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_header.blade.php", "line": 125}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047423.4714031, "duration": 0.10107, "duration_str": "101ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._header:125", "source": "view::layouts.admin.partials._header:125", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_header.blade.php&line=125", "ajax": false, "filename": "_header.blade.php", "line": "125"}, "connection": "dease_test", "start_percent": 74.08, "width_percent": 0.87}, {"sql": "select * from `modules` where `modules`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "layouts.admin.partials._header", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_header.blade.php", "line": 159}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047423.5804138, "duration": 0.09906999999999999, "duration_str": "99.07ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._header:159", "source": "view::layouts.admin.partials._header:159", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_header.blade.php&line=159", "ajax": false, "filename": "_header.blade.php", "line": "159"}, "connection": "dease_test", "start_percent": 74.95, "width_percent": 0.853}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (1) and `translations`.`translationable_type` = 'App\\Models\\Module' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\Module", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": "view", "name": "layouts.admin.partials._header", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_header.blade.php", "line": 159}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047423.685451, "duration": 0.10089000000000001, "duration_str": "101ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._header:159", "source": "view::layouts.admin.partials._header:159", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_header.blade.php&line=159", "ajax": false, "filename": "_header.blade.php", "line": "159"}, "connection": "dease_test", "start_percent": 75.802, "width_percent": 0.868}, {"sql": "select * from `modules` where `status` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "layouts.admin.partials._header", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_header.blade.php", "line": 178}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047423.8544872, "duration": 0.12827000000000002, "duration_str": "128ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._header:178", "source": "view::layouts.admin.partials._header:178", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_header.blade.php&line=178", "ajax": false, "filename": "_header.blade.php", "line": "178"}, "connection": "dease_test", "start_percent": 76.671, "width_percent": 1.104}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (1, 2, 5, 7) and `translations`.`translationable_type` = 'App\\Models\\Module' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\Module", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "layouts.admin.partials._header", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_header.blade.php", "line": 178}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047423.989941, "duration": 0.10905, "duration_str": "109ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._header:178", "source": "view::layouts.admin.partials._header:178", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_header.blade.php&line=178", "ajax": false, "filename": "_header.blade.php", "line": "178"}, "connection": "dease_test", "start_percent": 77.775, "width_percent": 0.938}, {"sql": "select * from `business_settings` where (`key` = 'logo') limit 1", "type": "query", "params": [], "bindings": ["logo"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 6}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047424.409462, "duration": 0.10066, "duration_str": "101ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:6", "source": "view::layouts.admin.partials._sidebar_grocery:6", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=6", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "6"}, "connection": "dease_test", "start_percent": 78.713, "width_percent": 0.866}, {"sql": "select count(*) as aggregate from `orders` where (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1", "type": "query", "params": [], "bindings": ["take_away", "delivery", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 97}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047424.687106, "duration": 0.10046, "duration_str": "100ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:97", "source": "view::layouts.admin.partials._sidebar_grocery:97", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=97", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "97"}, "connection": "dease_test", "start_percent": 79.579, "width_percent": 0.865}, {"sql": "select count(*) as aggregate from `orders` where created_at <> schedule_at and `scheduled` = '1' and (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1", "type": "query", "params": [], "bindings": ["1", "take_away", "delivery", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 110}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047424.802231, "duration": 0.10128, "duration_str": "101ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:110", "source": "view::layouts.admin.partials._sidebar_grocery:110", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=110", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "110"}, "connection": "dease_test", "start_percent": 80.444, "width_percent": 0.872}, {"sql": "select * from `business_settings` where `key` = 'first_order_status' limit 1", "type": "query", "params": [], "bindings": ["first_order_status"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 116}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047424.911315, "duration": 0.1039, "duration_str": "104ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:116", "source": "view::layouts.admin.partials._sidebar_grocery:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=116", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "116"}, "connection": "dease_test", "start_percent": 81.316, "width_percent": 0.894}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'initial' and ((created_at <> schedule_at and (`schedule_at` between '2025-06-04 15:30:25' and '2025-06-04 16:00:25') or `schedule_at` < '2025-06-04 15:30:25') or created_at = schedule_at) and (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1", "type": "query", "params": [], "bindings": ["initial", "2025-06-04 15:30:25", "2025-06-04 16:00:25", "2025-06-04 15:30:25", "take_away", "delivery", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 126}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047425.032145, "duration": 0.10012, "duration_str": "100ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:126", "source": "view::layouts.admin.partials._sidebar_grocery:126", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=126", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "126"}, "connection": "dease_test", "start_percent": 82.21, "width_percent": 0.862}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'pending' and ((created_at <> schedule_at and (`schedule_at` between '2025-06-04 15:30:25' and '2025-06-04 16:00:25') or `schedule_at` < '2025-06-04 15:30:25') or created_at = schedule_at) and (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1", "type": "query", "params": [], "bindings": ["pending", "2025-06-04 15:30:25", "2025-06-04 16:00:25", "2025-06-04 15:30:25", "take_away", "delivery", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 140}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047425.1509311, "duration": 0.10146, "duration_str": "101ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:140", "source": "view::layouts.admin.partials._sidebar_grocery:140", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=140", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "140"}, "connection": "dease_test", "start_percent": 83.071, "width_percent": 0.873}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'accepted' and ((created_at <> schedule_at and (`schedule_at` between '2025-06-04 15:30:25' and '2025-06-04 16:00:25') or `schedule_at` < '2025-06-04 15:30:25') or created_at = schedule_at) and (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1", "type": "query", "params": [], "bindings": ["accepted", "2025-06-04 15:30:25", "2025-06-04 16:00:25", "2025-06-04 15:30:25", "take_away", "delivery", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 154}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047425.2709432, "duration": 0.10828, "duration_str": "108ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:154", "source": "view::layouts.admin.partials._sidebar_grocery:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=154", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "154"}, "connection": "dease_test", "start_percent": 83.945, "width_percent": 0.932}, {"sql": "select count(*) as aggregate from `orders` where `order_status` in ('confirmed', 'processing', 'handover') and ((created_at <> schedule_at and (`schedule_at` between '2025-06-04 15:30:25' and '2025-06-04 16:00:25') or `schedule_at` < '2025-06-04 15:30:25') or created_at = schedule_at) and (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1", "type": "query", "params": [], "bindings": ["confirmed", "processing", "handover", "2025-06-04 15:30:25", "2025-06-04 16:00:25", "2025-06-04 15:30:25", "take_away", "delivery", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 167}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047425.394604, "duration": 0.10089000000000001, "duration_str": "101ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:167", "source": "view::layouts.admin.partials._sidebar_grocery:167", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=167", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "167"}, "connection": "dease_test", "start_percent": 84.876, "width_percent": 0.868}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'picked_up' and ((created_at <> schedule_at and (`schedule_at` between '2025-06-04 15:30:25' and '2025-06-04 16:00:25') or `schedule_at` < '2025-06-04 15:30:25') or created_at = schedule_at) and (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1", "type": "query", "params": [], "bindings": ["picked_up", "2025-06-04 15:30:25", "2025-06-04 16:00:25", "2025-06-04 15:30:25", "take_away", "delivery", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047425.511398, "duration": 0.1018, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:180", "source": "view::layouts.admin.partials._sidebar_grocery:180", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=180", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "180"}, "connection": "dease_test", "start_percent": 85.745, "width_percent": 0.876}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'delivered' and (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1", "type": "query", "params": [], "bindings": ["delivered", "take_away", "delivery", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 193}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047425.631631, "duration": 0.10164, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:193", "source": "view::layouts.admin.partials._sidebar_grocery:193", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=193", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "193"}, "connection": "dease_test", "start_percent": 86.621, "width_percent": 0.875}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'canceled' and (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1", "type": "query", "params": [], "bindings": ["canceled", "take_away", "delivery", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 206}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047425.748503, "duration": 0.10484, "duration_str": "105ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:206", "source": "view::layouts.admin.partials._sidebar_grocery:206", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=206", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "206"}, "connection": "dease_test", "start_percent": 87.495, "width_percent": 0.902}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'failed' and (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1", "type": "query", "params": [], "bindings": ["failed", "take_away", "delivery", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 219}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047425.869812, "duration": 0.1036, "duration_str": "104ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:219", "source": "view::layouts.admin.partials._sidebar_grocery:219", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=219", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "219"}, "connection": "dease_test", "start_percent": 88.398, "width_percent": 0.892}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'refunded' and (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1", "type": "query", "params": [], "bindings": ["refunded", "take_away", "delivery", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 232}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047425.988442, "duration": 0.10214, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:232", "source": "view::layouts.admin.partials._sidebar_grocery:232", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=232", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "232"}, "connection": "dease_test", "start_percent": 89.289, "width_percent": 0.879}, {"sql": "select count(*) as aggregate from `orders` where exists (select * from `offline_payments` where `orders`.`id` = `offline_payments`.`order_id`) and (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1", "type": "query", "params": [], "bindings": ["take_away", "delivery", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 246}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047426.106874, "duration": 0.10754000000000001, "duration_str": "108ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:246", "source": "view::layouts.admin.partials._sidebar_grocery:246", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=246", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "246"}, "connection": "dease_test", "start_percent": 90.168, "width_percent": 0.925}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'refund_requested' and (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1", "type": "query", "params": [], "bindings": null, "hints": null, "show_copy": false, "backtrace": [], "start": 1749047426.2391648, "duration": 0.10328, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "", "source": "", "xdebug_link": null, "connection": "dease_test", "start_percent": 91.094, "width_percent": 0.889}, {"sql": "select * from `business_settings` where `key` = 'product_approval' limit 1", "type": "query", "params": [], "bindings": null, "hints": null, "show_copy": false, "backtrace": [], "start": 1749047426.548002, "duration": 0.10195, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "", "source": "", "xdebug_link": null, "connection": "dease_test", "start_percent": 91.983, "width_percent": 0.877}, {"sql": "select * from `stores` where exists (select * from `vendors` where `stores`.`vendor_id` = `vendors`.`id` and `status` is null) and `module_id` = 1", "type": "query", "params": [], "bindings": null, "hints": null, "show_copy": false, "backtrace": [], "start": 1749047426.704628, "duration": 0.10467, "duration_str": "105ms", "memory": 0, "memory_str": null, "filename": "", "source": "", "xdebug_link": null, "connection": "dease_test", "start_percent": 92.86, "width_percent": 0.901}, {"sql": "select * from `business_settings` where (`key` = 'business_name') limit 1", "type": "query", "params": [], "bindings": null, "hints": null, "show_copy": false, "backtrace": [], "start": 1749047426.8742318, "duration": 0.10242, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "", "source": "", "xdebug_link": null, "connection": "dease_test", "start_percent": 93.761, "width_percent": 0.881}, {"sql": "select * from `business_settings` where (`key` = 'footer_text') limit 1", "type": "query", "params": [], "bindings": null, "hints": null, "show_copy": false, "backtrace": [], "start": 1749047426.9771478, "duration": 0.10454000000000001, "duration_str": "105ms", "memory": 0, "memory_str": null, "filename": "", "source": "", "xdebug_link": null, "connection": "dease_test", "start_percent": 94.642, "width_percent": 0.9}, {"sql": "select * from `modules` where `status` = 1", "type": "query", "params": [], "bindings": null, "hints": null, "show_copy": false, "backtrace": [], "start": 1749047427.122873, "duration": 0.1028, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "", "source": "", "xdebug_link": null, "connection": "dease_test", "start_percent": 95.542, "width_percent": 0.885}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (1, 2, 5, 7) and `translations`.`translationable_type` = 'App\\Models\\Module' and `locale` = 'en'", "type": "query", "params": [], "bindings": null, "hints": null, "show_copy": false, "backtrace": [], "start": 1749047427.2263658, "duration": 0.10346999999999999, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "", "source": "", "xdebug_link": null, "connection": "dease_test", "start_percent": 96.427, "width_percent": 0.89}, {"sql": "select * from `business_settings` where `key` = 'fcm_credentials' limit 1", "type": "query", "params": [], "bindings": null, "hints": null, "show_copy": false, "backtrace": [], "start": 1749047427.405348, "duration": 0.10321999999999999, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "", "source": "", "xdebug_link": null, "connection": "dease_test", "start_percent": 97.317, "width_percent": 0.888}, {"sql": "select * from `business_settings` where `key` = 'order_notification_type' limit 1", "type": "query", "params": [], "bindings": null, "hints": null, "show_copy": false, "backtrace": [], "start": 1749047427.509659, "duration": 0.10543000000000001, "duration_str": "105ms", "memory": 0, "memory_str": null, "filename": "", "source": "", "xdebug_link": null, "connection": "dease_test", "start_percent": 98.205, "width_percent": 0.907}, {"sql": "select * from `business_settings` where `key` = 'admin_order_notification' limit 1", "type": "query", "params": [], "bindings": null, "hints": null, "show_copy": false, "backtrace": [], "start": 1749047427.615701, "duration": 0.10311, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "", "source": "", "xdebug_link": null, "connection": "dease_test", "start_percent": 99.113, "width_percent": 0.887}]}, "models": {"data": {"App\\Models\\Translation": {"value": 46, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FTranslation.php&line=1", "ajax": false, "filename": "Translation.php", "line": "?"}}, "App\\Models\\User": {"value": 30, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\BusinessSetting": {"value": 15, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FBusinessSetting.php&line=1", "ajax": false, "filename": "BusinessSetting.php", "line": "?"}}, "App\\Models\\Module": {"value": 14, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FModule.php&line=1", "ajax": false, "filename": "Module.php", "line": "?"}}, "App\\Models\\DeliveryMan": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FDeliveryMan.php&line=1", "ajax": false, "filename": "DeliveryMan.php", "line": "?"}}, "App\\Models\\Item": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FItem.php&line=1", "ajax": false, "filename": "Item.php", "line": "?"}}, "App\\Models\\Store": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}, "App\\Models\\Zone": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FZone.php&line=1", "ajax": false, "filename": "Zone.php", "line": "?"}}, "App\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "App\\Models\\Currency": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "App\\Models\\AdminRole": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FAdminRole.php&line=1", "ajax": false, "filename": "AdminRole.php", "line": "?"}}}, "count": 124, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VLPBp8OZ5DPQQjor5cQrLq4YgrNA8oclmzN41QBj", "landing_site_direction": "ltr", "six_captcha": "m6Ydd", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/Admin-panel/admin\"\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "2", "site_direction": "ltr", "dash_params": "array:6 [\n  \"zone_id\" => \"all\"\n  \"module_id\" => 1\n  \"statistics_type\" => \"overall\"\n  \"user_overview\" => \"overall\"\n  \"commission_overview\" => \"this_year\"\n  \"business_overview\" => \"overall\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/admin", "status_code": "<pre class=sf-dump id=sf-dump-127535913 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-127535913\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2115222373 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2115222373\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1135945316 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1135945316\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1470336080 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">http://localhost/Admin-panel/login/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">en-US,en;q=0.9,fr-DZ;q=0.8,fr;q=0.7,ar-DZ;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1274 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ik5jRlo5KzllMDgvcERoQklrT3VUYkE9PSIsInZhbHVlIjoiVVFoUkF1aUFCQ1pIQjcxVWRKN0Y0bnJabkgrWFEwckhLVmF1NnQ3YkpPblJZVmhCL05ZK2h4c3Q3VGZVeWErRkNGeDJDY0RFMCt6NnM1d1E4cVlxRUpKN3lCUVFheWxUaXlCVWdpb21pRThQbmRyU3E0MEpvQlVoYVo3a25SeDNnaGlZR3B6YTJFc3J0KytQRlpHSE1CakpzbGR2OXRPeTN6ZmNUWmI4N2xnUlZCRW1LeHlPWElHcmthb3NkSnJ0R2NqUlUwTWZseW1zVzgrcTg3WEpzajFpWGdUMFZQMGd0eGN6ZlFDUWFBbz0iLCJtYWMiOiI1MjUzMzQ0OGQ1NTE1NTBkNzI5ZjQwZWE4OTFiNDJiNTM2ZjBiYjg4NTkxNDBhZjI4MjEwZmIwMzg3NzQ2ZWNhIiwidGFnIjoiIn0%3D; SL_G_WPT_TO=fr; XSRF-TOKEN=eyJpdiI6ImovT1VLeW1aSmNaNW00WmtPTklqaWc9PSIsInZhbHVlIjoiYWN0VzUwbXIrMmloK1IxbDdQMm95Y1QyM3Fkc1E1Nll6Z0tIVWNaSGw3ZVl5NTlzaVBhb1owOTcrVDVSaVUwQ2NYYVViVytqemhnSGx3bjhWeC9nMVZtSSs2NkU1amQ5YkNKR3JXdS9LZk1GR294b1A0WjBRTDc2YnQ1a3EvUGoiLCJtYWMiOiI0YWJjYjYxYjU0OGFjZTIyZjFhMjg0NmY2NzAyMjlmZjE2NTE1MGQxNWU2YjI0ZGNiY2RkMzk1ZTFiZmMwZmM2IiwidGFnIjoiIn0%3D; deaseapp_session=eyJpdiI6ImFFMzk3ZVAzQzBoL0JtSi9TRjk0eFE9PSIsInZhbHVlIjoiV2laYk9rTUdEUldwcE5uVTlVR2Z4SWJ5N0NHcEF1YU9PQnp6NktYNmhPUFlYZkNVT29wZndUa0pCTnlRRFlOdGFpUHkrMzhTeUMxNnVpUjVoV29NTFFMTDJDQmwwOWUzL21GY2lyUDNTSXphUkViTENjV2llTEF6dUltYlpCSVkiLCJtYWMiOiJmNjg5OTk0OGJlN2U4NmYxYWI0NTRkYjQ4Njc5NTJlZTM4ZDc1YzIzMzNhMTA0MmMwZDkwZTZmZmZhNWQ0ODNhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1470336080\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1632120243 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>SL_G_WPT_TO</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VLPBp8OZ5DPQQjor5cQrLq4YgrNA8oclmzN41QBj</span>\"\n  \"<span class=sf-dump-key>deaseapp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">U2ZZWlej1OcYr85cJVMgNe4TO1whPuiJSgYVa3MW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1632120243\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1518049460 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 04 Jun 2025 14:30:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"414 characters\">XSRF-TOKEN=eyJpdiI6IkxwZ3pFMTdXT2txeWwxaHIvaHlGY0E9PSIsInZhbHVlIjoiTjBSNzNNUlNSWmY3OVJKcHFCV2phR002cmRwTDFyb0RsNlhNeldwRmtVU1kvTmRRZXFxWWRRNnpSOXRjUUdCZGFZMU1BYkg2a1hLVjMvUHhpUVg2dXFHa2h3QnVOaERVNDdnUlRGK0wwU0JQdlUxdC9IdEF2OGoybXVDNFNhT1MiLCJtYWMiOiI1ZTcxNGYxN2YxYjkwNWQ0NmUzYWE2NDllZGIyMmI4M2YzOTEyNjU3NTg0MzE3MGFhYzg2ODQ1Y2RhNzZmYzU0IiwidGFnIjoiIn0%3D; expires=Wed, 04 Jun 2025 16:30:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"430 characters\">deaseapp_session=eyJpdiI6IlRVaFF3S3BMNkwzMllOZHJxSkk0VkE9PSIsInZhbHVlIjoiMThsQUxsRlZXZ2VpMi9rdTdxcGp0YVpDSEVTZFZZeGhxRGdxRy91WWF6MUVaVGN2VFFydG1BbXYzR0NmWW1WVVh1SW1tOFh4S2FtQ1VFbUJSdzRDQkhDc0lURnVReWJDd3NtK2sybXAyL1M0Z2VvanZRVDAvYXc2ZVBCY1lGZDMiLCJtYWMiOiIzNjkyYTgxNGQyNjQ0MmZlNTBlZWZmMDFhMmRiMDk4ZjRiMGRmZTYzMTU3YWRhM2FmMzVkOWQ5ODllN2YwYWI3IiwidGFnIjoiIn0%3D; expires=Wed, 04 Jun 2025 16:30:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkxwZ3pFMTdXT2txeWwxaHIvaHlGY0E9PSIsInZhbHVlIjoiTjBSNzNNUlNSWmY3OVJKcHFCV2phR002cmRwTDFyb0RsNlhNeldwRmtVU1kvTmRRZXFxWWRRNnpSOXRjUUdCZGFZMU1BYkg2a1hLVjMvUHhpUVg2dXFHa2h3QnVOaERVNDdnUlRGK0wwU0JQdlUxdC9IdEF2OGoybXVDNFNhT1MiLCJtYWMiOiI1ZTcxNGYxN2YxYjkwNWQ0NmUzYWE2NDllZGIyMmI4M2YzOTEyNjU3NTg0MzE3MGFhYzg2ODQ1Y2RhNzZmYzU0IiwidGFnIjoiIn0%3D; expires=Wed, 04-Jun-2025 16:30:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">deaseapp_session=eyJpdiI6IlRVaFF3S3BMNkwzMllOZHJxSkk0VkE9PSIsInZhbHVlIjoiMThsQUxsRlZXZ2VpMi9rdTdxcGp0YVpDSEVTZFZZeGhxRGdxRy91WWF6MUVaVGN2VFFydG1BbXYzR0NmWW1WVVh1SW1tOFh4S2FtQ1VFbUJSdzRDQkhDc0lURnVReWJDd3NtK2sybXAyL1M0Z2VvanZRVDAvYXc2ZVBCY1lGZDMiLCJtYWMiOiIzNjkyYTgxNGQyNjQ0MmZlNTBlZWZmMDFhMmRiMDk4ZjRiMGRmZTYzMTU3YWRhM2FmMzVkOWQ5ODllN2YwYWI3IiwidGFnIjoiIn0%3D; expires=Wed, 04-Jun-2025 16:30:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1518049460\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-216545719 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VLPBp8OZ5DPQQjor5cQrLq4YgrNA8oclmzN41QBj</span>\"\n  \"<span class=sf-dump-key>landing_site_direction</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ltr</span>\"\n  \"<span class=sf-dump-key>six_captcha</span>\" => \"<span class=sf-dump-str title=\"5 characters\">m6Ydd</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/Admin-panel/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>site_direction</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ltr</span>\"\n  \"<span class=sf-dump-key>dash_params</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>zone_id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">all</span>\"\n    \"<span class=sf-dump-key>module_id</span>\" => <span class=sf-dump-num>1</span>\n    \"<span class=sf-dump-key>statistics_type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">overall</span>\"\n    \"<span class=sf-dump-key>user_overview</span>\" => \"<span class=sf-dump-str title=\"7 characters\">overall</span>\"\n    \"<span class=sf-dump-key>commission_overview</span>\" => \"<span class=sf-dump-str title=\"9 characters\">this_year</span>\"\n    \"<span class=sf-dump-key>business_overview</span>\" => \"<span class=sf-dump-str title=\"7 characters\">overall</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-216545719\", {\"maxDepth\":0})</script>\n"}}