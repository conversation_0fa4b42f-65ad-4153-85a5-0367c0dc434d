{"__meta": {"id": "X50ffacd3be82e7ae0968ffc1c0ba208b", "datetime": "2025-06-04 15:28:53", "utime": 1749047333.939233, "method": "GET", "uri": "/Admin-panel/login/admin", "ip": "::1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749047330.22301, "end": 1749047333.939261, "duration": 3.7162508964538574, "duration_str": "3.72s", "measures": [{"label": "Booting", "start": 1749047330.22301, "relative_start": 0, "end": **********.410074, "relative_end": **********.410074, "duration": 2.1870639324188232, "duration_str": "2.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.410091, "relative_start": 2.1870808601379395, "end": 1749047333.939265, "relative_end": 4.0531158447265625e-06, "duration": 1.5291740894317627, "duration_str": "1.53s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51079648, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "auth.login", "param_count": null, "params": [], "start": **********.933957, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Admin-panel\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}}]}, "route": {"uri": "GET login/{tab}", "middleware": "web, guest:admin,vendor", "controller": "App\\Http\\Controllers\\LoginController@login", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FLoginController.php&line=38\" onclick=\"\">app/Http/Controllers/LoginController.php:38-92</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.5986, "accumulated_duration_str": "599ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `business_settings` where `key` = 'system_language' limit 1", "type": "query", "params": [], "bindings": ["system_language"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "middleware", "name": "localization", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\Localization.php", "line": 24}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 78}], "start": **********.438261, "duration": 0.09869, "duration_str": "98.69ms", "memory": 0, "memory_str": null, "filename": "localization:24", "source": "middleware::localization:24", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FMiddleware%2FLocalization.php&line=24", "ajax": false, "filename": "Localization.php", "line": "24"}, "connection": "dease_test", "start_percent": 0, "width_percent": 16.487}, {"sql": "select * from `business_settings` where `key` = 'system_language' limit 1", "type": "query", "params": [], "bindings": ["system_language"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/LoginController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\LoginController.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.55085, "duration": 0.10026, "duration_str": "100ms", "memory": 0, "memory_str": null, "filename": "LoginController.php:40", "source": "app/Http/Controllers/LoginController.php:40", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FLoginController.php&line=40", "ajax": false, "filename": "LoginController.php", "line": "40"}, "connection": "dease_test", "start_percent": 16.487, "width_percent": 16.749}, {"sql": "select `key`, `value` from `data_settings` where `key` in ('store_employee_login_url', 'store_login_url', 'admin_employee_login_url', 'admin_login_url')", "type": "query", "params": [], "bindings": ["store_employee_login_url", "store_login_url", "admin_employee_login_url", "admin_login_url"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/LoginController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\LoginController.php", "line": 50}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.659349, "duration": 0.0999, "duration_str": "99.9ms", "memory": 0, "memory_str": null, "filename": "LoginController.php:50", "source": "app/Http/Controllers/LoginController.php:50", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FLoginController.php&line=50", "ajax": false, "filename": "LoginController.php", "line": "50"}, "connection": "dease_test", "start_percent": 33.236, "width_percent": 16.689}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (0) and `translations`.`translationable_type` = 'App\\Models\\DataSetting' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\DataSetting", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/LoginController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\LoginController.php", "line": 50}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.769162, "duration": 0.09986, "duration_str": "99.86ms", "memory": 0, "memory_str": null, "filename": "LoginController.php:50", "source": "app/Http/Controllers/LoginController.php:50", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FLoginController.php&line=50", "ajax": false, "filename": "LoginController.php", "line": "50"}, "connection": "dease_test", "start_percent": 49.925, "width_percent": 16.682}, {"sql": "select * from `business_settings` where (`key` = 'logo') limit 1", "type": "query", "params": [], "bindings": ["logo"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "auth.login", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/auth/login.blade.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.947216, "duration": 0.09995, "duration_str": "99.95ms", "memory": 0, "memory_str": null, "filename": "auth.login:36", "source": "view::auth.login:36", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=36", "ajax": false, "filename": "login.blade.php", "line": "36"}, "connection": "dease_test", "start_percent": 66.607, "width_percent": 16.697}, {"sql": "select * from `business_settings` where `key` = 'recaptcha' limit 1", "type": "query", "params": [], "bindings": ["recaptcha"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\CentralLogics\\helpers.php", "line": 938}, {"index": 17, "namespace": "view", "name": "auth.login", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/auth/login.blade.php", "line": 129}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749047333.768699, "duration": 0.09994, "duration_str": "99.94ms", "memory": 0, "memory_str": null, "filename": "helpers.php:938", "source": "app/CentralLogics/helpers.php:938", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FCentralLogics%2Fhelpers.php&line=938", "ajax": false, "filename": "helpers.php", "line": "938"}, "connection": "dease_test", "start_percent": 83.304, "width_percent": 16.696}]}, "models": {"data": {"App\\Models\\BusinessSetting": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FBusinessSetting.php&line=1", "ajax": false, "filename": "BusinessSetting.php", "line": "?"}}, "App\\Models\\DataSetting": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FDataSetting.php&line=1", "ajax": false, "filename": "DataSetting.php", "line": "?"}}}, "count": 8, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VLPBp8OZ5DPQQjor5cQrLq4YgrNA8oclmzN41QBj", "landing_site_direction": "ltr", "six_captcha": "Gi6yL", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/Admin-panel/login/admin\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login/admin", "status_code": "<pre class=sf-dump id=sf-dump-47192612 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-47192612\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1753205674 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1753205674\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-277902831 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-277902831\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1492484109 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">http://localhost/Admin-panel/login/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">en-US,en;q=0.9,fr-DZ;q=0.8,fr;q=0.7,ar-DZ;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1274 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ik5jRlo5KzllMDgvcERoQklrT3VUYkE9PSIsInZhbHVlIjoiVVFoUkF1aUFCQ1pIQjcxVWRKN0Y0bnJabkgrWFEwckhLVmF1NnQ3YkpPblJZVmhCL05ZK2h4c3Q3VGZVeWErRkNGeDJDY0RFMCt6NnM1d1E4cVlxRUpKN3lCUVFheWxUaXlCVWdpb21pRThQbmRyU3E0MEpvQlVoYVo3a25SeDNnaGlZR3B6YTJFc3J0KytQRlpHSE1CakpzbGR2OXRPeTN6ZmNUWmI4N2xnUlZCRW1LeHlPWElHcmthb3NkSnJ0R2NqUlUwTWZseW1zVzgrcTg3WEpzajFpWGdUMFZQMGd0eGN6ZlFDUWFBbz0iLCJtYWMiOiI1MjUzMzQ0OGQ1NTE1NTBkNzI5ZjQwZWE4OTFiNDJiNTM2ZjBiYjg4NTkxNDBhZjI4MjEwZmIwMzg3NzQ2ZWNhIiwidGFnIjoiIn0%3D; SL_G_WPT_TO=fr; XSRF-TOKEN=eyJpdiI6IjhFWEtqR3VVRmdpbTVyZ2hvbnJtaHc9PSIsInZhbHVlIjoiVGlHNzJUcjZjcnk0ZFdINnpoclF3Rzg0Uk5LSEI5UUNjUFB3QnVJdm52UExpR0JJRjBTU3RIU28vSnE3a1BDYzZGMHVCT2RrYnQwaWdoT0F5QlhCK2pYOVdTY1NOVUEzem04eUFZQk5JSzk2M0Jha1Jsb3VhUzZyTHJPMWxuNUsiLCJtYWMiOiIwNWY0Y2M2NDEyNmEzMmViYzZkYzdlODc5MjI5YzY3ZDIxNmNkMmM1NWM4YWM2MDUxMTNmZTg5ZjI0NTAxMGRlIiwidGFnIjoiIn0%3D; deaseapp_session=eyJpdiI6ImFEVW5ocXVrdXIrQVhSam80ZHkwS1E9PSIsInZhbHVlIjoiVVRvMERsTW56TjJpa2ZyMlZkdDJpSmdkelgzV0RacVpaT2N1M3ZGUUVodjdPYWhMakxsbkpCRzZBcW42azV0NE5qamRhTTg0d2Z1bjIrNVdIMGg4TElHSjkxcVg1UEJMMC83ZlJhODBJTVBic2xhN1ZLNkFzaTBJM282V2UxMjAiLCJtYWMiOiJhNmExMzZjMWJhMTY3MjNlY2Q5NGVmY2E5ZmNmNmM0NjZjYWNiZWFkNmI2Y2E0MmQ3ZWM3MDJjOWZlMzc5YTE0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1492484109\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>SL_G_WPT_TO</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VLPBp8OZ5DPQQjor5cQrLq4YgrNA8oclmzN41QBj</span>\"\n  \"<span class=sf-dump-key>deaseapp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gxXjHQjJ4neLymX4UbYB7X7JOuHFJKeBRjPNcCnT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-145267937 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 04 Jun 2025 14:28:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"414 characters\">XSRF-TOKEN=eyJpdiI6ImM1TCtzUnpkaTV1TnhQZVRyUHNGNGc9PSIsInZhbHVlIjoiaXpvbGxuRzA0c2h0UUhUUlQxZEFrRldIVW1XeUlXbEIramlmdFZsZUJkeWlhVkRRTlRjVW9CK2NRWklRVXVtMnhZNFRKS3BBVXk3V0J6WnhwNzRsS21WZU1XRlF4amptdnpKRE1OMlpNU3hGLzBpNDkrUmlMbUlIRlJka3BXVEsiLCJtYWMiOiJkNjk0ZjE5MTQ4NzkxMDJmNDQ3MDUwZThlNjM3MDE2ODUzZDdkNzIwOWEzOTg5OWJlODJjNmQ5MzA3OTI5NzE4IiwidGFnIjoiIn0%3D; expires=Wed, 04 Jun 2025 16:28:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"430 characters\">deaseapp_session=eyJpdiI6InFidS9jTnFMcGpMTTc1YnQ5VDhob3c9PSIsInZhbHVlIjoiTVQvSWczS3U1Q2RkQlM5bEt3RThtaW1Rckx4UVc1ZG5DbXhqUXdZTVZ5UGtpcXFLdU9pRFNVb2xIdU5IMnA4ak1pZzkvWTJIOEpPOEI2c3kxVW40L1VydlQya0l6c0Y2Vi9NbllFS2FKeFBadkZUaEhpcjdJUjdSaDMrbkdabUQiLCJtYWMiOiJjNGY1NmE3YzRhMzIyNjU2YzllNDcwNDlkMDZjYzYwMzIyMWI0MTJjZWRhYzc0MDEwODE0OGY3MThlNWFiZWYxIiwidGFnIjoiIn0%3D; expires=Wed, 04 Jun 2025 16:28:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImM1TCtzUnpkaTV1TnhQZVRyUHNGNGc9PSIsInZhbHVlIjoiaXpvbGxuRzA0c2h0UUhUUlQxZEFrRldIVW1XeUlXbEIramlmdFZsZUJkeWlhVkRRTlRjVW9CK2NRWklRVXVtMnhZNFRKS3BBVXk3V0J6WnhwNzRsS21WZU1XRlF4amptdnpKRE1OMlpNU3hGLzBpNDkrUmlMbUlIRlJka3BXVEsiLCJtYWMiOiJkNjk0ZjE5MTQ4NzkxMDJmNDQ3MDUwZThlNjM3MDE2ODUzZDdkNzIwOWEzOTg5OWJlODJjNmQ5MzA3OTI5NzE4IiwidGFnIjoiIn0%3D; expires=Wed, 04-Jun-2025 16:28:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">deaseapp_session=eyJpdiI6InFidS9jTnFMcGpMTTc1YnQ5VDhob3c9PSIsInZhbHVlIjoiTVQvSWczS3U1Q2RkQlM5bEt3RThtaW1Rckx4UVc1ZG5DbXhqUXdZTVZ5UGtpcXFLdU9pRFNVb2xIdU5IMnA4ak1pZzkvWTJIOEpPOEI2c3kxVW40L1VydlQya0l6c0Y2Vi9NbllFS2FKeFBadkZUaEhpcjdJUjdSaDMrbkdabUQiLCJtYWMiOiJjNGY1NmE3YzRhMzIyNjU2YzllNDcwNDlkMDZjYzYwMzIyMWI0MTJjZWRhYzc0MDEwODE0OGY3MThlNWFiZWYxIiwidGFnIjoiIn0%3D; expires=Wed, 04-Jun-2025 16:28:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-145267937\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1650737531 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VLPBp8OZ5DPQQjor5cQrLq4YgrNA8oclmzN41QBj</span>\"\n  \"<span class=sf-dump-key>landing_site_direction</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ltr</span>\"\n  \"<span class=sf-dump-key>six_captcha</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Gi6yL</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://localhost/Admin-panel/login/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1650737531\", {\"maxDepth\":0})</script>\n"}}