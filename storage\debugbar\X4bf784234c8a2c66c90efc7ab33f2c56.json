{"__meta": {"id": "X4bf784234c8a2c66c90efc7ab33f2c56", "datetime": "2025-06-04 15:31:27", "utime": 1749047487.084121, "method": "GET", "uri": "/Admin-panel/login/admin-employee", "ip": "::1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749047482.385573, "end": 1749047487.084152, "duration": 4.6985790729522705, "duration_str": "4.7s", "measures": [{"label": "Booting", "start": 1749047482.385573, "relative_start": 0, "end": **********.939551, "relative_end": **********.939551, "duration": 2.553978204727173, "duration_str": "2.55s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.939583, "relative_start": 2.5540101528167725, "end": 1749047487.084156, "relative_end": 4.0531158447265625e-06, "duration": 2.1445729732513428, "duration_str": "2.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51081296, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "auth.login", "param_count": null, "params": [], "start": **********.658358, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Admin-panel\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}}]}, "route": {"uri": "GET login/{tab}", "middleware": "web, guest:admin,vendor", "controller": "App\\Http\\Controllers\\LoginController@login", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FLoginController.php&line=38\" onclick=\"\">app/Http/Controllers/LoginController.php:38-92</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.7061, "accumulated_duration_str": "706ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `business_settings` where `key` = 'system_language' limit 1", "type": "query", "params": [], "bindings": ["system_language"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "middleware", "name": "localization", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\Localization.php", "line": 24}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 78}], "start": **********.98905, "duration": 0.11643, "duration_str": "116ms", "memory": 0, "memory_str": null, "filename": "localization:24", "source": "middleware::localization:24", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FMiddleware%2FLocalization.php&line=24", "ajax": false, "filename": "Localization.php", "line": "24"}, "connection": "dease_test", "start_percent": 0, "width_percent": 16.489}, {"sql": "select * from `business_settings` where `key` = 'system_language' limit 1", "type": "query", "params": [], "bindings": ["system_language"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/LoginController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\LoginController.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.133344, "duration": 0.119, "duration_str": "119ms", "memory": 0, "memory_str": null, "filename": "LoginController.php:40", "source": "app/Http/Controllers/LoginController.php:40", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FLoginController.php&line=40", "ajax": false, "filename": "LoginController.php", "line": "40"}, "connection": "dease_test", "start_percent": 16.489, "width_percent": 16.853}, {"sql": "select `key`, `value` from `data_settings` where `key` in ('store_employee_login_url', 'store_login_url', 'admin_employee_login_url', 'admin_login_url')", "type": "query", "params": [], "bindings": ["store_employee_login_url", "store_login_url", "admin_employee_login_url", "admin_login_url"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/LoginController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\LoginController.php", "line": 50}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2659452, "duration": 0.*****************, "duration_str": "118ms", "memory": 0, "memory_str": null, "filename": "LoginController.php:50", "source": "app/Http/Controllers/LoginController.php:50", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FLoginController.php&line=50", "ajax": false, "filename": "LoginController.php", "line": "50"}, "connection": "dease_test", "start_percent": 33.342, "width_percent": 16.772}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (0) and `translations`.`translationable_type` = 'App\\Models\\DataSetting' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\DataSetting", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/LoginController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\LoginController.php", "line": 50}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.398015, "duration": 0.11713, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "LoginController.php:50", "source": "app/Http/Controllers/LoginController.php:50", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FLoginController.php&line=50", "ajax": false, "filename": "LoginController.php", "line": "50"}, "connection": "dease_test", "start_percent": 50.115, "width_percent": 16.588}, {"sql": "select * from `business_settings` where (`key` = 'logo') limit 1", "type": "query", "params": [], "bindings": ["logo"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "auth.login", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/auth/login.blade.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.683068, "duration": 0.1173, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "auth.login:36", "source": "view::auth.login:36", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=36", "ajax": false, "filename": "login.blade.php", "line": "36"}, "connection": "dease_test", "start_percent": 66.703, "width_percent": 16.612}, {"sql": "select * from `business_settings` where `key` = 'recaptcha' limit 1", "type": "query", "params": [], "bindings": ["recaptcha"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\CentralLogics\\helpers.php", "line": 938}, {"index": 17, "namespace": "view", "name": "auth.login", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/auth/login.blade.php", "line": 129}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749047486.8684, "duration": 0.11781, "duration_str": "118ms", "memory": 0, "memory_str": null, "filename": "helpers.php:938", "source": "app/CentralLogics/helpers.php:938", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FCentralLogics%2Fhelpers.php&line=938", "ajax": false, "filename": "helpers.php", "line": "938"}, "connection": "dease_test", "start_percent": 83.315, "width_percent": 16.685}]}, "models": {"data": {"App\\Models\\BusinessSetting": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FBusinessSetting.php&line=1", "ajax": false, "filename": "BusinessSetting.php", "line": "?"}}, "App\\Models\\DataSetting": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FDataSetting.php&line=1", "ajax": false, "filename": "DataSetting.php", "line": "?"}}}, "count": 8, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VLPBp8OZ5DPQQjor5cQrLq4YgrNA8oclmzN41QBj", "landing_site_direction": "ltr", "six_captcha": "HGEMZ", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/Admin-panel/login/admin-employee\"\n]", "site_direction": "ltr", "dash_params": "array:6 [\n  \"zone_id\" => \"all\"\n  \"module_id\" => 1\n  \"statistics_type\" => \"overall\"\n  \"user_overview\" => \"overall\"\n  \"commission_overview\" => \"this_year\"\n  \"business_overview\" => \"overall\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login/admin-employee", "status_code": "<pre class=sf-dump id=sf-dump-189664025 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-189664025\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1794699091 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1794699091\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-813069634 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-813069634\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1419074599 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"49 characters\">http://localhost/Admin-panel/login/admin-employee</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">en-US,en;q=0.9,fr-DZ;q=0.8,fr;q=0.7,ar-DZ;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1274 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ik5jRlo5KzllMDgvcERoQklrT3VUYkE9PSIsInZhbHVlIjoiVVFoUkF1aUFCQ1pIQjcxVWRKN0Y0bnJabkgrWFEwckhLVmF1NnQ3YkpPblJZVmhCL05ZK2h4c3Q3VGZVeWErRkNGeDJDY0RFMCt6NnM1d1E4cVlxRUpKN3lCUVFheWxUaXlCVWdpb21pRThQbmRyU3E0MEpvQlVoYVo3a25SeDNnaGlZR3B6YTJFc3J0KytQRlpHSE1CakpzbGR2OXRPeTN6ZmNUWmI4N2xnUlZCRW1LeHlPWElHcmthb3NkSnJ0R2NqUlUwTWZseW1zVzgrcTg3WEpzajFpWGdUMFZQMGd0eGN6ZlFDUWFBbz0iLCJtYWMiOiI1MjUzMzQ0OGQ1NTE1NTBkNzI5ZjQwZWE4OTFiNDJiNTM2ZjBiYjg4NTkxNDBhZjI4MjEwZmIwMzg3NzQ2ZWNhIiwidGFnIjoiIn0%3D; SL_G_WPT_TO=fr; XSRF-TOKEN=eyJpdiI6IkV6TndkQVBjK0VBbkRveTM4ek1VNnc9PSIsInZhbHVlIjoiQnNKSFFNR2h6SEMvWHdlVnlTNUJJUTlZTjVUZno1MDVvR0hOZmtLcFRqUU8zOUJFZEx0QVBkV3NrOVVKR08xMUIxVWJDRHlTUHBzWXhKTGo4QVdQaW1QYXBmVjVveXFVQkNaYzBHVUhoTy91OThYbkp2S3ZjUUc1Y2tSeHl5S3AiLCJtYWMiOiJmNTQ3ZDdiZDg4YTE0ZjZiZTEwZGU2ZjJhNzJiZjEzOTI1NjIwZjI2ZTAzMWUwNDI0OGUzYzA3M2JkMjhlMjVlIiwidGFnIjoiIn0%3D; deaseapp_session=eyJpdiI6IkxmeCttZWwxYWRabVVwYUtZNHg2UXc9PSIsInZhbHVlIjoibWo0dWYxYnBiTUZhVjBtM2FlclZZVEpNN3R4NGMrNlltYy9WTnBDWlF4M21IZVFWb09XKzZQMXo1TE1mT3pHakJTb0ovM3k5MGV1blVIZzlqdWlzc0FrSUtiT20rT2hwUlVUSkhrUEQyK3dsMHVPQ2xkd1EwTzdFbVpRQkt2WDEiLCJtYWMiOiI3ZTZjODE3NzUwODVjM2FhMThmNzFmOWQ0N2M2YWMxNWZkZWM3MzBjZDJlNGNiMzBjZmFkNGFjMzNmNWJlNWRkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1419074599\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2144546065 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>SL_G_WPT_TO</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VLPBp8OZ5DPQQjor5cQrLq4YgrNA8oclmzN41QBj</span>\"\n  \"<span class=sf-dump-key>deaseapp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">U2ZZWlej1OcYr85cJVMgNe4TO1whPuiJSgYVa3MW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2144546065\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2120688473 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 04 Jun 2025 14:31:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"414 characters\">XSRF-TOKEN=eyJpdiI6InJrd0J0bHBZNEZNazFYc2U2NTF4V0E9PSIsInZhbHVlIjoiRk9CdHl6dmtpUFovaWEwNkM1dU90N3NzQzRiNmF2dFVvSGgzOUVrakZNRkRaSVNhZndEakJxYXJpdndwcTdNNGxjV0Fxb2RiZnRha043dGNmaWVWckQxMDBRbCt0WThBM2lnTUcwYTgzWFkzRzlaK0VSWkJ1My93ZjA5YzFLbHUiLCJtYWMiOiJlNmM2MTczZWVkY2RhZWIzYTczMzdjZGQxZTZhMzczOWM2OGE0NGJhZTc4MTQxNDgyYWYyYjc4ZGY5ZWI0YTRjIiwidGFnIjoiIn0%3D; expires=Wed, 04 Jun 2025 16:31:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"430 characters\">deaseapp_session=eyJpdiI6InhPd0oyV0ZLd2I5V0l5dmovRGtSdEE9PSIsInZhbHVlIjoiVDNZWHdxM1d4V3FXMFdZZ2RMelNNazVLRHRoNlFyR3pKVk8rR1NVNjZFUnNIUmNwenozSk96ZkdabmlYMzVEVGJzZUNkL3E1aVlhWVBDdGM1dnRRWjlDV1ZISkdvTFYvczFjY3RxNSthWHBrTzJoNFdLSThQZGp4K1hwV0tkSkQiLCJtYWMiOiIxZTI4MTJlNDM3ZDI5NjcyMzNlMDgxMWIxNzUxNzdlNTI2NWY5YThkMzJiYjZjOGQ0NzEyN2UwMWRhZjU5NjE4IiwidGFnIjoiIn0%3D; expires=Wed, 04 Jun 2025 16:31:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InJrd0J0bHBZNEZNazFYc2U2NTF4V0E9PSIsInZhbHVlIjoiRk9CdHl6dmtpUFovaWEwNkM1dU90N3NzQzRiNmF2dFVvSGgzOUVrakZNRkRaSVNhZndEakJxYXJpdndwcTdNNGxjV0Fxb2RiZnRha043dGNmaWVWckQxMDBRbCt0WThBM2lnTUcwYTgzWFkzRzlaK0VSWkJ1My93ZjA5YzFLbHUiLCJtYWMiOiJlNmM2MTczZWVkY2RhZWIzYTczMzdjZGQxZTZhMzczOWM2OGE0NGJhZTc4MTQxNDgyYWYyYjc4ZGY5ZWI0YTRjIiwidGFnIjoiIn0%3D; expires=Wed, 04-Jun-2025 16:31:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">deaseapp_session=eyJpdiI6InhPd0oyV0ZLd2I5V0l5dmovRGtSdEE9PSIsInZhbHVlIjoiVDNZWHdxM1d4V3FXMFdZZ2RMelNNazVLRHRoNlFyR3pKVk8rR1NVNjZFUnNIUmNwenozSk96ZkdabmlYMzVEVGJzZUNkL3E1aVlhWVBDdGM1dnRRWjlDV1ZISkdvTFYvczFjY3RxNSthWHBrTzJoNFdLSThQZGp4K1hwV0tkSkQiLCJtYWMiOiIxZTI4MTJlNDM3ZDI5NjcyMzNlMDgxMWIxNzUxNzdlNTI2NWY5YThkMzJiYjZjOGQ0NzEyN2UwMWRhZjU5NjE4IiwidGFnIjoiIn0%3D; expires=Wed, 04-Jun-2025 16:31:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2120688473\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-197918302 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VLPBp8OZ5DPQQjor5cQrLq4YgrNA8oclmzN41QBj</span>\"\n  \"<span class=sf-dump-key>landing_site_direction</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ltr</span>\"\n  \"<span class=sf-dump-key>six_captcha</span>\" => \"<span class=sf-dump-str title=\"5 characters\">HGEMZ</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"49 characters\">http://localhost/Admin-panel/login/admin-employee</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>site_direction</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ltr</span>\"\n  \"<span class=sf-dump-key>dash_params</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>zone_id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">all</span>\"\n    \"<span class=sf-dump-key>module_id</span>\" => <span class=sf-dump-num>1</span>\n    \"<span class=sf-dump-key>statistics_type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">overall</span>\"\n    \"<span class=sf-dump-key>user_overview</span>\" => \"<span class=sf-dump-str title=\"7 characters\">overall</span>\"\n    \"<span class=sf-dump-key>commission_overview</span>\" => \"<span class=sf-dump-str title=\"9 characters\">this_year</span>\"\n    \"<span class=sf-dump-key>business_overview</span>\" => \"<span class=sf-dump-str title=\"7 characters\">overall</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-197918302\", {\"maxDepth\":0})</script>\n"}}