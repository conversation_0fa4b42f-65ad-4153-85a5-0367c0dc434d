{"__meta": {"id": "X39d2bcb9ce3bd09be7372070764cd466", "datetime": "2025-06-04 18:20:43", "utime": **********.441739, "method": "GET", "uri": "/Admin-panel/admin/message/list", "ip": "::1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749057639.713636, "end": **********.441765, "duration": 3.7281291484832764, "duration_str": "3.73s", "measures": [{"label": "Booting", "start": 1749057639.713636, "relative_start": 0, "end": **********.407699, "relative_end": **********.407699, "duration": 2.694063186645508, "duration_str": "2.69s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.407731, "relative_start": 2.6940951347351074, "end": **********.441769, "relative_end": 3.814697265625e-06, "duration": 1.0340378284454346, "duration_str": "1.03s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 30787672, "peak_usage_str": "29MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "admin-views.messages.data", "param_count": null, "params": [], "start": **********.412057, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Admin-panel\\resources\\views/admin-views/messages/data.blade.phpadmin-views.messages.data", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Fadmin-views%2Fmessages%2Fdata.blade.php&line=1", "ajax": false, "filename": "data.blade.php", "line": "?"}}]}, "route": {"uri": "GET admin/message/list", "middleware": "web, admin, current-module, module:customer_management", "controller": "App\\Http\\Controllers\\Admin\\ConversationController@list", "as": "admin.message.list", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin/message", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FConversationController.php&line=18\" onclick=\"\">app/Http/Controllers/Admin/ConversationController.php:18-45</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.86593, "accumulated_duration_str": "866ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `business_settings` where `key` = 'system_language' limit 1", "type": "query", "params": [], "bindings": ["system_language"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "middleware", "name": "localization", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\Localization.php", "line": 24}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 78}], "start": **********.4597168, "duration": 0.1221, "duration_str": "122ms", "memory": 0, "memory_str": null, "filename": "localization:24", "source": "middleware::localization:24", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FMiddleware%2FLocalization.php&line=24", "ajax": false, "filename": "Localization.php", "line": "24"}, "connection": "dease_test", "start_percent": 0, "width_percent": 14.1}, {"sql": "select * from `admins` where `id` = 5 limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": "middleware", "name": "admin", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\AdminMiddleware.php", "line": 21}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": "middleware", "name": "localization", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\Localization.php", "line": 60}], "start": **********.604058, "duration": 0.12706, "duration_str": "127ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "dease_test", "start_percent": 14.1, "width_percent": 14.673}, {"sql": "select * from `modules` where `status` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "middleware", "name": "current-module", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\CurrentModule.php", "line": 31}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "admin", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\AdminMiddleware.php", "line": 25}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "localization", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\Localization.php", "line": 60}], "start": **********.741054, "duration": 0.12344, "duration_str": "123ms", "memory": 0, "memory_str": null, "filename": "current-module:31", "source": "middleware::current-module:31", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FMiddleware%2FCurrentModule.php&line=31", "ajax": false, "filename": "CurrentModule.php", "line": "31"}, "connection": "dease_test", "start_percent": 28.774, "width_percent": 14.255}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (1, 2, 5, 7) and `translations`.`translationable_type` = 'App\\Models\\Module' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\Module", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "current-module", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\CurrentModule.php", "line": 31}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": "middleware", "name": "admin", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\AdminMiddleware.php", "line": 25}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": "middleware", "name": "localization", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\Localization.php", "line": 60}], "start": **********.8732991, "duration": 0.12318000000000001, "duration_str": "123ms", "memory": 0, "memory_str": null, "filename": "current-module:31", "source": "middleware::current-module:31", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FMiddleware%2FCurrentModule.php&line=31", "ajax": false, "filename": "CurrentModule.php", "line": "31"}, "connection": "dease_test", "start_percent": 43.029, "width_percent": 14.225}, {"sql": "select * from `admin_roles` where `admin_roles`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\CentralLogics\\helpers.php", "line": 2196}, {"index": 22, "namespace": "middleware", "name": "module", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\ModulePermissionMiddleware.php", "line": 20}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": "middleware", "name": "current-module", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\CurrentModule.php", "line": 57}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.005342, "duration": 0.12312999999999999, "duration_str": "123ms", "memory": 0, "memory_str": null, "filename": "helpers.php:2196", "source": "app/CentralLogics/helpers.php:2196", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FCentralLogics%2Fhelpers.php&line=2196", "ajax": false, "filename": "helpers.php", "line": "2196"}, "connection": "dease_test", "start_percent": 57.254, "width_percent": 14.219}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (4) and `translations`.`translationable_type` = 'App\\Models\\AdminRole' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\AdminRole", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\CentralLogics\\helpers.php", "line": 2196}, {"index": 27, "namespace": "middleware", "name": "module", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\ModulePermissionMiddleware.php", "line": 20}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 29, "namespace": "middleware", "name": "current-module", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\CurrentModule.php", "line": 57}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1351259, "duration": 0.12343000000000001, "duration_str": "123ms", "memory": 0, "memory_str": null, "filename": "helpers.php:2196", "source": "app/CentralLogics/helpers.php:2196", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FCentralLogics%2Fhelpers.php&line=2196", "ajax": false, "filename": "helpers.php", "line": "2196"}, "connection": "dease_test", "start_percent": 71.473, "width_percent": 14.254}, {"sql": "select count(*) as aggregate from `conversations` where (`sender_type` = 'admin' or `receiver_type` = 'admin')", "type": "query", "params": [], "bindings": ["admin", "admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/ConversationController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\ConversationController.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.26625, "duration": 0.12359, "duration_str": "124ms", "memory": 0, "memory_str": null, "filename": "ConversationController.php:37", "source": "app/Http/Controllers/Admin/ConversationController.php:37", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FConversationController.php&line=37", "ajax": false, "filename": "ConversationController.php", "line": "37"}, "connection": "dease_test", "start_percent": 85.727, "width_percent": 14.273}]}, "models": {"data": {"App\\Models\\Translation": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FTranslation.php&line=1", "ajax": false, "filename": "Translation.php", "line": "?"}}, "App\\Models\\Module": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FModule.php&line=1", "ajax": false, "filename": "Module.php", "line": "?"}}, "App\\Models\\BusinessSetting": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FBusinessSetting.php&line=1", "ajax": false, "filename": "BusinessSetting.php", "line": "?"}}, "App\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "App\\Models\\AdminRole": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FAdminRole.php&line=1", "ajax": false, "filename": "AdminRole.php", "line": "?"}}}, "count": 16, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0pqieAzfH74oJerE5OhbnxrPyCObJe7jMhZxcftT", "site_direction": "ltr", "_previous": "array:1 [\n  \"url\" => \"http://localhost/Admin-panel/admin/transactions/store/withdraw_list\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "landing_site_direction": "ltr", "six_captcha": "<PERSON><PERSON><PERSON>", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "5", "dash_params": "array:6 [\n  \"zone_id\" => \"all\"\n  \"module_id\" => 1\n  \"statistics_type\" => \"overall\"\n  \"user_overview\" => \"overall\"\n  \"commission_overview\" => \"this_year\"\n  \"business_overview\" => \"overall\"\n]"}, "request": {"path_info": "/admin/message/list", "status_code": "<pre class=sf-dump id=sf-dump-127234457 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-127234457\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1702628827 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1702628827\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1501812719 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1501812719\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1884040977 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"67 characters\">http://localhost/Admin-panel/admin/transactions/store/withdraw_list</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">en-US,en;q=0.9,fr-DZ;q=0.8,fr;q=0.7,ar-DZ;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1274 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ik5jRlo5KzllMDgvcERoQklrT3VUYkE9PSIsInZhbHVlIjoiVVFoUkF1aUFCQ1pIQjcxVWRKN0Y0bnJabkgrWFEwckhLVmF1NnQ3YkpPblJZVmhCL05ZK2h4c3Q3VGZVeWErRkNGeDJDY0RFMCt6NnM1d1E4cVlxRUpKN3lCUVFheWxUaXlCVWdpb21pRThQbmRyU3E0MEpvQlVoYVo3a25SeDNnaGlZR3B6YTJFc3J0KytQRlpHSE1CakpzbGR2OXRPeTN6ZmNUWmI4N2xnUlZCRW1LeHlPWElHcmthb3NkSnJ0R2NqUlUwTWZseW1zVzgrcTg3WEpzajFpWGdUMFZQMGd0eGN6ZlFDUWFBbz0iLCJtYWMiOiI1MjUzMzQ0OGQ1NTE1NTBkNzI5ZjQwZWE4OTFiNDJiNTM2ZjBiYjg4NTkxNDBhZjI4MjEwZmIwMzg3NzQ2ZWNhIiwidGFnIjoiIn0%3D; SL_G_WPT_TO=fr; XSRF-TOKEN=eyJpdiI6IlF4QVl6NFdrM3lUOVNXMUNpV1hLa2c9PSIsInZhbHVlIjoiYjc5T0ttdUNqeDNGRzRiS1FhY0NwelhnT2xLU2h1dmJKMUFrUzk3aUtYRWQwSW1ZNnNaUnNPQkJiUG94cGFES01wbWtIa0FYUVVsbkdNMjZlUVpzV1Rza1Qvd0MzY1U4d3VmRkROaDg1T1hVQ2p5ZEFqU0NWUy9yV20vYWxyUmMiLCJtYWMiOiI4NDE3YzQyNWNiMDQxMGFiMTM4M2MwMzJlOGMzZDVkMzdhMzFjMzQ0OWY0MTRhMDNiMDg2NWZjYjVmOTlkZWMwIiwidGFnIjoiIn0%3D; deaseapp_session=eyJpdiI6Im1jWXU0bmNZUnJlSFR2eUdxUUo2dEE9PSIsInZhbHVlIjoieFdIN1ZDOGtLTHBKaGx5NWFaVXpnSDRGUm96WDdRU09QMXFoUlIxdG85Y3orVXU5NmtsQnd0ZmYzNEVIa2dpMlk3QWZSNTdDMkE3NUNxNWpycFFwODU0VGlBVGYwQ243Zmx6T0hXK2lGS3JRTU5vK1BCUzY3VS9TVXE1cloxa2UiLCJtYWMiOiIxMmVhMzdhM2JlM2E3ODZjZjM4OWU3MDljOWNmODNjZDY3ZTVjZTM1NzE0YTRmNzc3NjFlNzg4ZGQwN2FmYWM5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1884040977\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-283812992 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>SL_G_WPT_TO</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0pqieAzfH74oJerE5OhbnxrPyCObJe7jMhZxcftT</span>\"\n  \"<span class=sf-dump-key>deaseapp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">V9hQnNfbej1xZs6As7bY8YDI4v6i5lCcrxfyz96y</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-283812992\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-633983999 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 04 Jun 2025 17:20:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"414 characters\">XSRF-TOKEN=eyJpdiI6ImJPb2tzMGN0UW9QbXU4REdmNWZUQ3c9PSIsInZhbHVlIjoiYURkZy9xajVNZ1lxREduWGVncWpMNFR4RXRyeTZCZUpaMlpxOExkYlhIQ0taQ3Z6ZnhGVFhwemRpZ1lMWTljTDVrVEJZQlZ4cVVIV25qWXFGb0hGSlV0cmZJNlhzaFpqei9LbHdSbUVZRm5JRzQ5NG9RRUdRb2I1M1NiUk1pbTUiLCJtYWMiOiI3Y2ZlMWJkZjRjZDRmYjBjMDEwNGUwOTdjNjUwZjM1MDc1YTg2ZjI2OGZhNDUyZTJmMmY3OGFiMmU3OWQzMDhiIiwidGFnIjoiIn0%3D; expires=Wed, 04 Jun 2025 19:20:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"430 characters\">deaseapp_session=eyJpdiI6IkdoQXlLckhKS1hxRVg4VWozRjF4NHc9PSIsInZhbHVlIjoicVk2NWR5QXNPVVIwcHc5enJaMlBPYktIem1QcGIvamg1UHlkRjZYcjdzd0pFazhnMitHMmN0ekxMUkRUdjFzUk9QckhVdjY3dTdMbkp4SHM2NHZJT1k1QUQ2LzFtWWpZTlZKOXB2ekljd0ZORUNKY3g4amdGdXU4aVQ4QmtFVWIiLCJtYWMiOiIxYWYxYWE5YmQ1OTg4ODAzMDQ3OTJmZGMzNjM4ZjdiM2JhZTJmY2FiMDFlMWMwYzBjODNmNGNjNjQxNzg4Y2U3IiwidGFnIjoiIn0%3D; expires=Wed, 04 Jun 2025 19:20:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImJPb2tzMGN0UW9QbXU4REdmNWZUQ3c9PSIsInZhbHVlIjoiYURkZy9xajVNZ1lxREduWGVncWpMNFR4RXRyeTZCZUpaMlpxOExkYlhIQ0taQ3Z6ZnhGVFhwemRpZ1lMWTljTDVrVEJZQlZ4cVVIV25qWXFGb0hGSlV0cmZJNlhzaFpqei9LbHdSbUVZRm5JRzQ5NG9RRUdRb2I1M1NiUk1pbTUiLCJtYWMiOiI3Y2ZlMWJkZjRjZDRmYjBjMDEwNGUwOTdjNjUwZjM1MDc1YTg2ZjI2OGZhNDUyZTJmMmY3OGFiMmU3OWQzMDhiIiwidGFnIjoiIn0%3D; expires=Wed, 04-Jun-2025 19:20:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">deaseapp_session=eyJpdiI6IkdoQXlLckhKS1hxRVg4VWozRjF4NHc9PSIsInZhbHVlIjoicVk2NWR5QXNPVVIwcHc5enJaMlBPYktIem1QcGIvamg1UHlkRjZYcjdzd0pFazhnMitHMmN0ekxMUkRUdjFzUk9QckhVdjY3dTdMbkp4SHM2NHZJT1k1QUQ2LzFtWWpZTlZKOXB2ekljd0ZORUNKY3g4amdGdXU4aVQ4QmtFVWIiLCJtYWMiOiIxYWYxYWE5YmQ1OTg4ODAzMDQ3OTJmZGMzNjM4ZjdiM2JhZTJmY2FiMDFlMWMwYzBjODNmNGNjNjQxNzg4Y2U3IiwidGFnIjoiIn0%3D; expires=Wed, 04-Jun-2025 19:20:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-633983999\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1988825269 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0pqieAzfH74oJerE5OhbnxrPyCObJe7jMhZxcftT</span>\"\n  \"<span class=sf-dump-key>site_direction</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ltr</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"67 characters\">http://localhost/Admin-panel/admin/transactions/store/withdraw_list</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>landing_site_direction</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ltr</span>\"\n  \"<span class=sf-dump-key>six_captcha</span>\" => \"<span class=sf-dump-str title=\"5 characters\">CyKOr</span>\"\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>dash_params</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>zone_id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">all</span>\"\n    \"<span class=sf-dump-key>module_id</span>\" => <span class=sf-dump-num>1</span>\n    \"<span class=sf-dump-key>statistics_type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">overall</span>\"\n    \"<span class=sf-dump-key>user_overview</span>\" => \"<span class=sf-dump-str title=\"7 characters\">overall</span>\"\n    \"<span class=sf-dump-key>commission_overview</span>\" => \"<span class=sf-dump-str title=\"9 characters\">this_year</span>\"\n    \"<span class=sf-dump-key>business_overview</span>\" => \"<span class=sf-dump-str title=\"7 characters\">overall</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1988825269\", {\"maxDepth\":0})</script>\n"}}