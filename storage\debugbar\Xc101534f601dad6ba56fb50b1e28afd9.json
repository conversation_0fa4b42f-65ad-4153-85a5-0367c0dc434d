{"__meta": {"id": "Xc101534f601dad6ba56fb50b1e28afd9", "datetime": "2025-06-04 18:20:21", "utime": 1749057621.590162, "method": "GET", "uri": "/Admin-panel/admin", "ip": "::1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749057603.724757, "end": 1749057621.590186, "duration": 17.865429162979126, "duration_str": "17.87s", "measures": [{"label": "Booting", "start": 1749057603.724757, "relative_start": 0, "end": **********.293803, "relative_end": **********.293803, "duration": 2.5690460205078125, "duration_str": "2.57s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.293821, "relative_start": 2.569064140319824, "end": 1749057621.590189, "relative_end": 2.86102294921875e-06, "duration": 15.296367883682251, "duration_str": "15.3s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53588488, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 6, "templates": [{"name": "admin-views.dashboard-grocery", "param_count": null, "params": [], "start": 1749057615.001032, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Admin-panel\\resources\\views/admin-views/dashboard-grocery.blade.phpadmin-views.dashboard-grocery", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Fadmin-views%2Fdashboard-grocery.blade.php&line=1", "ajax": false, "filename": "dashboard-grocery.blade.php", "line": "?"}}, {"name": "layouts.admin.app", "param_count": null, "params": [], "start": 1749057615.168442, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/app.blade.phplayouts.admin.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "layouts.admin.partials._front-settings", "param_count": null, "params": [], "start": 1749057615.426225, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_front-settings.blade.phplayouts.admin.partials._front-settings", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_front-settings.blade.php&line=1", "ajax": false, "filename": "_front-settings.blade.php", "line": "?"}}, {"name": "layouts.admin.partials._header", "param_count": null, "params": [], "start": 1749057615.429679, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_header.blade.phplayouts.admin.partials._header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_header.blade.php&line=1", "ajax": false, "filename": "_header.blade.php", "line": "?"}}, {"name": "layouts.admin.partials._sidebar_grocery", "param_count": null, "params": [], "start": 1749057617.610479, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.phplayouts.admin.partials._sidebar_grocery", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=1", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "?"}}, {"name": "layouts.admin.partials._footer", "param_count": null, "params": [], "start": 1749057620.556637, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_footer.blade.phplayouts.admin.partials._footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_footer.blade.php&line=1", "ajax": false, "filename": "_footer.blade.php", "line": "?"}}]}, "route": {"uri": "GET admin", "middleware": "web, admin, current-module", "controller": "App\\Http\\Controllers\\Admin\\DashboardController@dashboard", "as": "admin.dashboard", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=217\" onclick=\"\">app/Http/Controllers/Admin/DashboardController.php:217-239</a>"}, "queries": {"nb_statements": 103, "nb_failed_statements": 0, "accumulated_duration": 12.12867, "accumulated_duration_str": "12.13s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 3 queries only show the query. Limit can be raised in the config. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select * from `business_settings` where `key` = 'system_language' limit 1", "type": "query", "params": [], "bindings": ["system_language"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "middleware", "name": "localization", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\Localization.php", "line": 24}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 78}], "start": **********.349715, "duration": 0.11748, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "localization:24", "source": "middleware::localization:24", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FMiddleware%2FLocalization.php&line=24", "ajax": false, "filename": "Localization.php", "line": "24"}, "connection": "dease_test", "start_percent": 0, "width_percent": 0.969}, {"sql": "select * from `admins` where `id` = 5 limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": "middleware", "name": "admin", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\AdminMiddleware.php", "line": 21}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": "middleware", "name": "localization", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\Localization.php", "line": 60}], "start": **********.503733, "duration": 0.11884, "duration_str": "119ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "dease_test", "start_percent": 0.969, "width_percent": 0.98}, {"sql": "select * from `modules` where `status` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "middleware", "name": "current-module", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\CurrentModule.php", "line": 31}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "admin", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\AdminMiddleware.php", "line": 25}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "localization", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\Localization.php", "line": 60}], "start": **********.6312041, "duration": 0.1197, "duration_str": "120ms", "memory": 0, "memory_str": null, "filename": "current-module:31", "source": "middleware::current-module:31", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FMiddleware%2FCurrentModule.php&line=31", "ajax": false, "filename": "CurrentModule.php", "line": "31"}, "connection": "dease_test", "start_percent": 1.948, "width_percent": 0.987}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (1, 2, 5, 7) and `translations`.`translationable_type` = 'App\\Models\\Module' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\Module", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "current-module", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\CurrentModule.php", "line": 31}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": "middleware", "name": "admin", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\AdminMiddleware.php", "line": 25}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": "middleware", "name": "localization", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\Localization.php", "line": 60}], "start": **********.7632139, "duration": 0.12104000000000001, "duration_str": "121ms", "memory": 0, "memory_str": null, "filename": "current-module:31", "source": "middleware::current-module:31", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FMiddleware%2FCurrentModule.php&line=31", "ajax": false, "filename": "CurrentModule.php", "line": "31"}, "connection": "dease_test", "start_percent": 2.935, "width_percent": 0.998}, {"sql": "select * from `users`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 447}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.899385, "duration": 0.*****************, "duration_str": "118ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:447", "source": "app/Http/Controllers/Admin/DashboardController.php:447", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=447", "ajax": false, "filename": "DashboardController.php", "line": "447"}, "connection": "dease_test", "start_percent": 3.933, "width_percent": 0.977}, {"sql": "select count(*) as aggregate from `orders` where `delivery_man_id` is null and `order_type` in ('delivery', 'parcel') and `order_status` not in ('delivered', 'failed', 'canceled', 'refund_requested', 'refund_request_canceled', 'refunded') and `module_id` = 1 and (`order_type` = 'take_away' or `order_type` = 'delivery') and ((created_at <> schedule_at and (`schedule_at` between '2025-06-04 18:20:07' and '2025-06-04 18:50:07') or `schedule_at` < '2025-06-04 18:20:07') or created_at = schedule_at) and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["delivery", "parcel", "delivered", "failed", "canceled", "refund_requested", "refund_request_canceled", "refunded", "1", "take_away", "delivery", "2025-06-04 18:20:07", "2025-06-04 18:50:07", "2025-06-04 18:20:07", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 485}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.029932, "duration": 0.11971, "duration_str": "120ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:485", "source": "app/Http/Controllers/Admin/DashboardController.php:485", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=485", "ajax": false, "filename": "DashboardController.php", "line": "485"}, "connection": "dease_test", "start_percent": 4.91, "width_percent": 0.987}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'accepted' and `module_id` = 1 and (`order_type` = 'take_away' or `order_type` = 'delivery') and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["accepted", "1", "take_away", "delivery", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 486}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.156579, "duration": 0.11927, "duration_str": "119ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:486", "source": "app/Http/Controllers/Admin/DashboardController.php:486", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=486", "ajax": false, "filename": "DashboardController.php", "line": "486"}, "connection": "dease_test", "start_percent": 5.897, "width_percent": 0.983}, {"sql": "select count(*) as aggregate from `orders` where `order_status` in ('confirmed', 'processing', 'handover') and `module_id` = 1 and (`order_type` = 'take_away' or `order_type` = 'delivery') and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["confirmed", "processing", "handover", "1", "take_away", "delivery", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 487}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.282455, "duration": 0.11905, "duration_str": "119ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:487", "source": "app/Http/Controllers/Admin/DashboardController.php:487", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=487", "ajax": false, "filename": "DashboardController.php", "line": "487"}, "connection": "dease_test", "start_percent": 6.88, "width_percent": 0.982}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'picked_up' and `module_id` = 1 and (`order_type` = 'take_away' or `order_type` = 'delivery') and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["picked_up", "1", "take_away", "delivery", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 488}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.408355, "duration": 0.11712, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:488", "source": "app/Http/Controllers/Admin/DashboardController.php:488", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=488", "ajax": false, "filename": "DashboardController.php", "line": "488"}, "connection": "dease_test", "start_percent": 7.862, "width_percent": 0.966}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'delivered' and `module_id` = 1 and (`order_type` = 'take_away' or `order_type` = 'delivery') and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["delivered", "1", "take_away", "delivery", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 489}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.532245, "duration": 0.11837, "duration_str": "118ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:489", "source": "app/Http/Controllers/Admin/DashboardController.php:489", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=489", "ajax": false, "filename": "DashboardController.php", "line": "489"}, "connection": "dease_test", "start_percent": 8.828, "width_percent": 0.976}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'canceled' and `module_id` = 1 and (`order_type` = 'take_away' or `order_type` = 'delivery') and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["canceled", "1", "take_away", "delivery", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 490}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.657884, "duration": 0.11982, "duration_str": "120ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:490", "source": "app/Http/Controllers/Admin/DashboardController.php:490", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=490", "ajax": false, "filename": "DashboardController.php", "line": "490"}, "connection": "dease_test", "start_percent": 9.804, "width_percent": 0.988}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'failed' and `module_id` = 1 and (`order_type` = 'take_away' or `order_type` = 'delivery') and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["failed", "1", "take_away", "delivery", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 491}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.785306, "duration": 0.11721, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:491", "source": "app/Http/Controllers/Admin/DashboardController.php:491", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=491", "ajax": false, "filename": "DashboardController.php", "line": "491"}, "connection": "dease_test", "start_percent": 10.792, "width_percent": 0.966}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'refunded' and `module_id` = 1 and (`order_type` = 'take_away' or `order_type` = 'delivery') and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["refunded", "1", "take_away", "delivery", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 492}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.909654, "duration": 0.11921, "duration_str": "119ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:492", "source": "app/Http/Controllers/Admin/DashboardController.php:492", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=492", "ajax": false, "filename": "DashboardController.php", "line": "492"}, "connection": "dease_test", "start_percent": 11.758, "width_percent": 0.983}, {"sql": "select count(*) as aggregate from `orders` where `module_id` = 1 and (`order_type` = 'take_away' or `order_type` = 'delivery') and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["1", "take_away", "delivery", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 493}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0355492, "duration": 0.11985, "duration_str": "120ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:493", "source": "app/Http/Controllers/Admin/DashboardController.php:493", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=493", "ajax": false, "filename": "DashboardController.php", "line": "493"}, "connection": "dease_test", "start_percent": 12.741, "width_percent": 0.988}, {"sql": "select count(*) as aggregate from `items` where `module_id` = 1 and exists (select * from `stores` where `items`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["1", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 494}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.16185, "duration": 0.11767, "duration_str": "118ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:494", "source": "app/Http/Controllers/Admin/DashboardController.php:494", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=494", "ajax": false, "filename": "DashboardController.php", "line": "494"}, "connection": "dease_test", "start_percent": 13.729, "width_percent": 0.97}, {"sql": "select count(*) as aggregate from `stores` where `module_id` = 1 and `zone_id` = 2", "type": "query", "params": [], "bindings": ["1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 495}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.286587, "duration": 0.11822, "duration_str": "118ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:495", "source": "app/Http/Controllers/Admin/DashboardController.php:495", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=495", "ajax": false, "filename": "DashboardController.php", "line": "495"}, "connection": "dease_test", "start_percent": 14.699, "width_percent": 0.975}, {"sql": "select count(*) as aggregate from `orders` where `module_id` = 1 and date(`schedule_at`) >= '2025-05-05' and (`order_type` = 'take_away' or `order_type` = 'delivery') and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["1", "2025-05-05", "take_away", "delivery", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 497}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.411517, "duration": 0.*****************, "duration_str": "118ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:497", "source": "app/Http/Controllers/Admin/DashboardController.php:497", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=497", "ajax": false, "filename": "DashboardController.php", "line": "497"}, "connection": "dease_test", "start_percent": 15.674, "width_percent": 0.972}, {"sql": "select count(*) as aggregate from `items` where `module_id` = 1 and date(`created_at`) >= '2025-05-05' and exists (select * from `stores` where `items`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["1", "2025-05-05", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 498}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.536493, "duration": 0.*****************, "duration_str": "120ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:498", "source": "app/Http/Controllers/Admin/DashboardController.php:498", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=498", "ajax": false, "filename": "DashboardController.php", "line": "498"}, "connection": "dease_test", "start_percent": 16.646, "width_percent": 0.986}, {"sql": "select count(*) as aggregate from `stores` where `module_id` = 1 and date(`created_at`) >= '2025-05-05' and `zone_id` = 2", "type": "query", "params": [], "bindings": ["1", "2025-05-05", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 499}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6622648, "duration": 0.11612, "duration_str": "116ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:499", "source": "app/Http/Controllers/Admin/DashboardController.php:499", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=499", "ajax": false, "filename": "DashboardController.php", "line": "499"}, "connection": "dease_test", "start_percent": 17.631, "width_percent": 0.957}, {"sql": "select count(*) as aggregate from `users` where date(`created_at`) >= '2025-05-05'", "type": "query", "params": [], "bindings": ["2025-05-05"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 500}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7844858, "duration": 0.11804, "duration_str": "118ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:500", "source": "app/Http/Controllers/Admin/DashboardController.php:500", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=500", "ajax": false, "filename": "DashboardController.php", "line": "500"}, "connection": "dease_test", "start_percent": 18.589, "width_percent": 0.973}, {"sql": "select count(*) as aggregate from `users` where `id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 593}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 629}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.910631, "duration": 0.1178, "duration_str": "118ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:593", "source": "app/Http/Controllers/Admin/DashboardController.php:593", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=593", "ajax": false, "filename": "DashboardController.php", "line": "593"}, "connection": "dease_test", "start_percent": 19.562, "width_percent": 0.971}, {"sql": "select count(*) as aggregate from `stores` where `module_id` = 1 and `id` is not null and `zone_id` = 2", "type": "query", "params": [], "bindings": ["1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 594}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 629}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0366979, "duration": 0.11681, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:594", "source": "app/Http/Controllers/Admin/DashboardController.php:594", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=594", "ajax": false, "filename": "DashboardController.php", "line": "594"}, "connection": "dease_test", "start_percent": 20.533, "width_percent": 0.963}, {"sql": "select count(*) as aggregate from `delivery_men` where `application_status` = 'approved' and `type` = 'zone_wise' and `zone_id` = 2", "type": "query", "params": [], "bindings": ["approved", "zone_wise", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 595}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 629}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.1597478, "duration": 0.11663, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:595", "source": "app/Http/Controllers/Admin/DashboardController.php:595", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=595", "ajax": false, "filename": "DashboardController.php", "line": "595"}, "connection": "dease_test", "start_percent": 21.496, "width_percent": 0.962}, {"sql": "select `store_id`, COUNT(store_id) as count from `wishlists` where exists (select * from `stores` where `wishlists`.`store_id` = `stores`.`id` and `zone_id` = 2) and exists (select * from `stores` where `wishlists`.`store_id` = `stores`.`id` and `module_id` = 1 and `zone_id` = 2) group by `store_id` order by `count` desc limit 6", "type": "query", "params": [], "bindings": ["2", "1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 643}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.2866771, "duration": 0.11673, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:643", "source": "app/Http/Controllers/Admin/DashboardController.php:643", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=643", "ajax": false, "filename": "DashboardController.php", "line": "643"}, "connection": "dease_test", "start_percent": 22.458, "width_percent": 0.962}, {"sql": "select * from `items` where exists (select * from `stores` where `items`.`store_id` = `stores`.`id` and `module_id` = 1 and `zone_id` = 2) order by `order_count` desc limit 6", "type": "query", "params": [], "bindings": ["1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 657}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.4099479, "duration": 0.12013, "duration_str": "120ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:657", "source": "app/Http/Controllers/Admin/DashboardController.php:657", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=657", "ajax": false, "filename": "DashboardController.php", "line": "657"}, "connection": "dease_test", "start_percent": 23.42, "width_percent": 0.99}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (4, 5) and `translations`.`translationable_type` = 'App\\Models\\Item' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\Item", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 657}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.538604, "duration": 0.11802, "duration_str": "118ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:657", "source": "app/Http/Controllers/Admin/DashboardController.php:657", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=657", "ajax": false, "filename": "DashboardController.php", "line": "657"}, "connection": "dease_test", "start_percent": 24.411, "width_percent": 0.973}, {"sql": "select * from `items` where exists (select * from `stores` where `items`.`store_id` = `stores`.`id` and `module_id` = 1 and `zone_id` = 2) order by `rating_count` desc limit 6", "type": "query", "params": [], "bindings": ["1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 671}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.662688, "duration": 0.12501, "duration_str": "125ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:671", "source": "app/Http/Controllers/Admin/DashboardController.php:671", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=671", "ajax": false, "filename": "DashboardController.php", "line": "671"}, "connection": "dease_test", "start_percent": 25.384, "width_percent": 1.031}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (4, 5) and `translations`.`translationable_type` = 'App\\Models\\Item' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\Item", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 671}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.794041, "duration": 0.*****************, "duration_str": "118ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:671", "source": "app/Http/Controllers/Admin/DashboardController.php:671", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=671", "ajax": false, "filename": "DashboardController.php", "line": "671"}, "connection": "dease_test", "start_percent": 26.414, "width_percent": 0.976}, {"sql": "select `delivery_men`.*, (select count(*) from `orders` where `delivery_men`.`id` = `orders`.`delivery_man_id` and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)) as `orders_count` from `delivery_men` where `type` = 'zone_wise' and `zone_id` = 2 order by `orders_count` desc limit 6", "type": "query", "params": [], "bindings": ["2", "2", "zone_wise", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 679}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.920238, "duration": 0.1198, "duration_str": "120ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:679", "source": "app/Http/Controllers/Admin/DashboardController.php:679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=679", "ajax": false, "filename": "DashboardController.php", "line": "679"}, "connection": "dease_test", "start_percent": 27.39, "width_percent": 0.988}, {"sql": "select * from `users` order by `order_count` desc limit 6", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 686}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.04629, "duration": 0.11866, "duration_str": "119ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:686", "source": "app/Http/Controllers/Admin/DashboardController.php:686", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=686", "ajax": false, "filename": "DashboardController.php", "line": "686"}, "connection": "dease_test", "start_percent": 28.378, "width_percent": 0.978}, {"sql": "select * from `stores` where `module_id` = 1 and `zone_id` = 2 order by `order_count` desc limit 6", "type": "query", "params": [], "bindings": ["1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 696}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.171503, "duration": 0.11853, "duration_str": "119ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:696", "source": "app/Http/Controllers/Admin/DashboardController.php:696", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=696", "ajax": false, "filename": "DashboardController.php", "line": "696"}, "connection": "dease_test", "start_percent": 29.356, "width_percent": 0.977}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (6, 7, 21, 23) and `translations`.`translationable_type` = 'App\\Models\\Store' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\Store", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 696}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.296722, "duration": 0.11935, "duration_str": "119ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:696", "source": "app/Http/Controllers/Admin/DashboardController.php:696", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=696", "ajax": false, "filename": "DashboardController.php", "line": "696"}, "connection": "dease_test", "start_percent": 30.334, "width_percent": 0.984}, {"sql": "select sum(`order_amount`) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '01' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "01", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 744}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.508436, "duration": 0.11711, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:744", "source": "app/Http/Controllers/Admin/DashboardController.php:744", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=744", "ajax": false, "filename": "DashboardController.php", "line": "744"}, "connection": "dease_test", "start_percent": 31.318, "width_percent": 0.966}, {"sql": "select sum(admin_commission + admin_expense - delivery_fee_comission) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '01' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "01", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 753}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.632127, "duration": 0.11815, "duration_str": "118ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:753", "source": "app/Http/Controllers/Admin/DashboardController.php:753", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=753", "ajax": false, "filename": "DashboardController.php", "line": "753"}, "connection": "dease_test", "start_percent": 32.283, "width_percent": 0.974}, {"sql": "select sum(`delivery_fee_comission`) as aggregate from `order_transactions` where `module_id` = 1 and month(`created_at`) = '01' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["1", "01", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 761}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.75648, "duration": 0.11892, "duration_str": "119ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:761", "source": "app/Http/Controllers/Admin/DashboardController.php:761", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=761", "ajax": false, "filename": "DashboardController.php", "line": "761"}, "connection": "dease_test", "start_percent": 33.257, "width_percent": 0.98}, {"sql": "select sum(`order_amount`) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '02' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "02", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 744}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.883363, "duration": 0.11626, "duration_str": "116ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:744", "source": "app/Http/Controllers/Admin/DashboardController.php:744", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=744", "ajax": false, "filename": "DashboardController.php", "line": "744"}, "connection": "dease_test", "start_percent": 34.238, "width_percent": 0.959}, {"sql": "select sum(admin_commission + admin_expense - delivery_fee_comission) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '02' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "02", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 753}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.006137, "duration": 0.11819, "duration_str": "118ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:753", "source": "app/Http/Controllers/Admin/DashboardController.php:753", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=753", "ajax": false, "filename": "DashboardController.php", "line": "753"}, "connection": "dease_test", "start_percent": 35.196, "width_percent": 0.974}, {"sql": "select sum(`delivery_fee_comission`) as aggregate from `order_transactions` where `module_id` = 1 and month(`created_at`) = '02' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["1", "02", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 761}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.1306891, "duration": 0.11723, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:761", "source": "app/Http/Controllers/Admin/DashboardController.php:761", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=761", "ajax": false, "filename": "DashboardController.php", "line": "761"}, "connection": "dease_test", "start_percent": 36.171, "width_percent": 0.967}, {"sql": "select sum(`order_amount`) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '03' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "03", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 744}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.2553082, "duration": 0.11722, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:744", "source": "app/Http/Controllers/Admin/DashboardController.php:744", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=744", "ajax": false, "filename": "DashboardController.php", "line": "744"}, "connection": "dease_test", "start_percent": 37.137, "width_percent": 0.966}, {"sql": "select sum(admin_commission + admin_expense - delivery_fee_comission) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '03' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "03", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 753}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.3791451, "duration": 0.11758, "duration_str": "118ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:753", "source": "app/Http/Controllers/Admin/DashboardController.php:753", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=753", "ajax": false, "filename": "DashboardController.php", "line": "753"}, "connection": "dease_test", "start_percent": 38.104, "width_percent": 0.969}, {"sql": "select sum(`delivery_fee_comission`) as aggregate from `order_transactions` where `module_id` = 1 and month(`created_at`) = '03' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["1", "03", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 761}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.503207, "duration": 0.11641, "duration_str": "116ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:761", "source": "app/Http/Controllers/Admin/DashboardController.php:761", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=761", "ajax": false, "filename": "DashboardController.php", "line": "761"}, "connection": "dease_test", "start_percent": 39.073, "width_percent": 0.96}, {"sql": "select sum(`order_amount`) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '04' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "04", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 744}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.6260898, "duration": 0.11733, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:744", "source": "app/Http/Controllers/Admin/DashboardController.php:744", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=744", "ajax": false, "filename": "DashboardController.php", "line": "744"}, "connection": "dease_test", "start_percent": 40.033, "width_percent": 0.967}, {"sql": "select sum(admin_commission + admin_expense - delivery_fee_comission) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '04' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "04", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 753}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.7500248, "duration": 0.11735, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:753", "source": "app/Http/Controllers/Admin/DashboardController.php:753", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=753", "ajax": false, "filename": "DashboardController.php", "line": "753"}, "connection": "dease_test", "start_percent": 41.001, "width_percent": 0.968}, {"sql": "select sum(`delivery_fee_comission`) as aggregate from `order_transactions` where `module_id` = 1 and month(`created_at`) = '04' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["1", "04", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 761}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.874729, "duration": 0.11625, "duration_str": "116ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:761", "source": "app/Http/Controllers/Admin/DashboardController.php:761", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=761", "ajax": false, "filename": "DashboardController.php", "line": "761"}, "connection": "dease_test", "start_percent": 41.968, "width_percent": 0.958}, {"sql": "select sum(`order_amount`) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '05' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "05", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 744}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.9977689, "duration": 0.11734, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:744", "source": "app/Http/Controllers/Admin/DashboardController.php:744", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=744", "ajax": false, "filename": "DashboardController.php", "line": "744"}, "connection": "dease_test", "start_percent": 42.927, "width_percent": 0.967}, {"sql": "select sum(admin_commission + admin_expense - delivery_fee_comission) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '05' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "05", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 753}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.1213448, "duration": 0.11664, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:753", "source": "app/Http/Controllers/Admin/DashboardController.php:753", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=753", "ajax": false, "filename": "DashboardController.php", "line": "753"}, "connection": "dease_test", "start_percent": 43.894, "width_percent": 0.962}, {"sql": "select sum(`delivery_fee_comission`) as aggregate from `order_transactions` where `module_id` = 1 and month(`created_at`) = '05' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["1", "05", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 761}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.2444408, "duration": 0.1177, "duration_str": "118ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:761", "source": "app/Http/Controllers/Admin/DashboardController.php:761", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=761", "ajax": false, "filename": "DashboardController.php", "line": "761"}, "connection": "dease_test", "start_percent": 44.856, "width_percent": 0.97}, {"sql": "select sum(`order_amount`) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '06' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "06", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 744}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.368546, "duration": 0.11763, "duration_str": "118ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:744", "source": "app/Http/Controllers/Admin/DashboardController.php:744", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=744", "ajax": false, "filename": "DashboardController.php", "line": "744"}, "connection": "dease_test", "start_percent": 45.826, "width_percent": 0.97}, {"sql": "select sum(admin_commission + admin_expense - delivery_fee_comission) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '06' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "06", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 753}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.49419, "duration": 0.11674, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:753", "source": "app/Http/Controllers/Admin/DashboardController.php:753", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=753", "ajax": false, "filename": "DashboardController.php", "line": "753"}, "connection": "dease_test", "start_percent": 46.796, "width_percent": 0.963}, {"sql": "select sum(`delivery_fee_comission`) as aggregate from `order_transactions` where `module_id` = 1 and month(`created_at`) = '06' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["1", "06", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 761}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.617063, "duration": 0.1171, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:761", "source": "app/Http/Controllers/Admin/DashboardController.php:761", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=761", "ajax": false, "filename": "DashboardController.php", "line": "761"}, "connection": "dease_test", "start_percent": 47.758, "width_percent": 0.965}, {"sql": "select sum(`order_amount`) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '07' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "07", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 744}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.740429, "duration": 0.11696, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:744", "source": "app/Http/Controllers/Admin/DashboardController.php:744", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=744", "ajax": false, "filename": "DashboardController.php", "line": "744"}, "connection": "dease_test", "start_percent": 48.724, "width_percent": 0.964}, {"sql": "select sum(admin_commission + admin_expense - delivery_fee_comission) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '07' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "07", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 753}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.86423, "duration": 0.11621, "duration_str": "116ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:753", "source": "app/Http/Controllers/Admin/DashboardController.php:753", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=753", "ajax": false, "filename": "DashboardController.php", "line": "753"}, "connection": "dease_test", "start_percent": 49.688, "width_percent": 0.958}, {"sql": "select sum(`delivery_fee_comission`) as aggregate from `order_transactions` where `module_id` = 1 and month(`created_at`) = '07' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["1", "07", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 761}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.9880612, "duration": 0.11529, "duration_str": "115ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:761", "source": "app/Http/Controllers/Admin/DashboardController.php:761", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=761", "ajax": false, "filename": "DashboardController.php", "line": "761"}, "connection": "dease_test", "start_percent": 50.646, "width_percent": 0.951}, {"sql": "select sum(`order_amount`) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '08' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "08", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 744}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.109987, "duration": 0.11617, "duration_str": "116ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:744", "source": "app/Http/Controllers/Admin/DashboardController.php:744", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=744", "ajax": false, "filename": "DashboardController.php", "line": "744"}, "connection": "dease_test", "start_percent": 51.597, "width_percent": 0.958}, {"sql": "select sum(admin_commission + admin_expense - delivery_fee_comission) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '08' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "08", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 753}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.232614, "duration": 0.11544, "duration_str": "115ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:753", "source": "app/Http/Controllers/Admin/DashboardController.php:753", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=753", "ajax": false, "filename": "DashboardController.php", "line": "753"}, "connection": "dease_test", "start_percent": 52.555, "width_percent": 0.952}, {"sql": "select sum(`delivery_fee_comission`) as aggregate from `order_transactions` where `module_id` = 1 and month(`created_at`) = '08' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["1", "08", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 761}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.354228, "duration": 0.11587, "duration_str": "116ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:761", "source": "app/Http/Controllers/Admin/DashboardController.php:761", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=761", "ajax": false, "filename": "DashboardController.php", "line": "761"}, "connection": "dease_test", "start_percent": 53.507, "width_percent": 0.955}, {"sql": "select sum(`order_amount`) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '09' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "09", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 744}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.476394, "duration": 0.11573, "duration_str": "116ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:744", "source": "app/Http/Controllers/Admin/DashboardController.php:744", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=744", "ajax": false, "filename": "DashboardController.php", "line": "744"}, "connection": "dease_test", "start_percent": 54.462, "width_percent": 0.954}, {"sql": "select sum(admin_commission + admin_expense - delivery_fee_comission) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '09' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "09", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 753}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.6002712, "duration": 0.11579, "duration_str": "116ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:753", "source": "app/Http/Controllers/Admin/DashboardController.php:753", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=753", "ajax": false, "filename": "DashboardController.php", "line": "753"}, "connection": "dease_test", "start_percent": 55.416, "width_percent": 0.955}, {"sql": "select sum(`delivery_fee_comission`) as aggregate from `order_transactions` where `module_id` = 1 and month(`created_at`) = '09' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["1", "09", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 761}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.7222579, "duration": 0.11593, "duration_str": "116ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:761", "source": "app/Http/Controllers/Admin/DashboardController.php:761", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=761", "ajax": false, "filename": "DashboardController.php", "line": "761"}, "connection": "dease_test", "start_percent": 56.371, "width_percent": 0.956}, {"sql": "select sum(`order_amount`) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '10' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "10", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 744}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.844781, "duration": 0.11859, "duration_str": "119ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:744", "source": "app/Http/Controllers/Admin/DashboardController.php:744", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=744", "ajax": false, "filename": "DashboardController.php", "line": "744"}, "connection": "dease_test", "start_percent": 57.327, "width_percent": 0.978}, {"sql": "select sum(admin_commission + admin_expense - delivery_fee_comission) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '10' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "10", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 753}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.970002, "duration": 0.11642, "duration_str": "116ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:753", "source": "app/Http/Controllers/Admin/DashboardController.php:753", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=753", "ajax": false, "filename": "DashboardController.php", "line": "753"}, "connection": "dease_test", "start_percent": 58.304, "width_percent": 0.96}, {"sql": "select sum(`delivery_fee_comission`) as aggregate from `order_transactions` where `module_id` = 1 and month(`created_at`) = '10' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["1", "10", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 761}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.093435, "duration": 0.11695, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:761", "source": "app/Http/Controllers/Admin/DashboardController.php:761", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=761", "ajax": false, "filename": "DashboardController.php", "line": "761"}, "connection": "dease_test", "start_percent": 59.264, "width_percent": 0.964}, {"sql": "select sum(`order_amount`) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '11' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "11", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 744}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.2167072, "duration": 0.11684, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:744", "source": "app/Http/Controllers/Admin/DashboardController.php:744", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=744", "ajax": false, "filename": "DashboardController.php", "line": "744"}, "connection": "dease_test", "start_percent": 60.229, "width_percent": 0.963}, {"sql": "select sum(admin_commission + admin_expense - delivery_fee_comission) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '11' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "11", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 753}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.339997, "duration": 0.11975, "duration_str": "120ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:753", "source": "app/Http/Controllers/Admin/DashboardController.php:753", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=753", "ajax": false, "filename": "DashboardController.php", "line": "753"}, "connection": "dease_test", "start_percent": 61.192, "width_percent": 0.987}, {"sql": "select sum(`delivery_fee_comission`) as aggregate from `order_transactions` where `module_id` = 1 and month(`created_at`) = '11' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["1", "11", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 761}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.4682012, "duration": 0.11617, "duration_str": "116ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:761", "source": "app/Http/Controllers/Admin/DashboardController.php:761", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=761", "ajax": false, "filename": "DashboardController.php", "line": "761"}, "connection": "dease_test", "start_percent": 62.179, "width_percent": 0.958}, {"sql": "select sum(`order_amount`) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '12' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "12", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 744}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.5925841, "duration": 0.11686, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:744", "source": "app/Http/Controllers/Admin/DashboardController.php:744", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=744", "ajax": false, "filename": "DashboardController.php", "line": "744"}, "connection": "dease_test", "start_percent": 63.137, "width_percent": 0.964}, {"sql": "select sum(admin_commission + admin_expense - delivery_fee_comission) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '12' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "12", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 753}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.717653, "duration": 0.11797, "duration_str": "118ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:753", "source": "app/Http/Controllers/Admin/DashboardController.php:753", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=753", "ajax": false, "filename": "DashboardController.php", "line": "753"}, "connection": "dease_test", "start_percent": 64.101, "width_percent": 0.973}, {"sql": "select sum(`delivery_fee_comission`) as aggregate from `order_transactions` where `module_id` = 1 and month(`created_at`) = '12' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["1", "12", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 761}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.844818, "duration": 0.11949, "duration_str": "119ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:761", "source": "app/Http/Controllers/Admin/DashboardController.php:761", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=761", "ajax": false, "filename": "DashboardController.php", "line": "761"}, "connection": "dease_test", "start_percent": 65.073, "width_percent": 0.985}, {"sql": "select * from `business_settings` where (`key` = 'business_name') limit 1", "type": "query", "params": [], "bindings": ["business_name"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "admin-views.dashboard-grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/admin-views/dashboard-grocery.blade.php", "line": 1}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749057615.005095, "duration": 0.11748, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "admin-views.dashboard-grocery:1", "source": "view::admin-views.dashboard-grocery:1", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Fadmin-views%2Fdashboard-grocery.blade.php&line=1", "ajax": false, "filename": "dashboard-grocery.blade.php", "line": "1"}, "connection": "dease_test", "start_percent": 66.058, "width_percent": 0.969}, {"sql": "select * from `business_settings` where `key` = 'country' limit 1", "type": "query", "params": [], "bindings": ["country"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.app", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/app.blade.php", "line": 4}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749057615.17352, "duration": 0.11903, "duration_str": "119ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.app:4", "source": "view::layouts.admin.app:4", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fapp.blade.php&line=4", "ajax": false, "filename": "app.blade.php", "line": "4"}, "connection": "dease_test", "start_percent": 67.027, "width_percent": 0.981}, {"sql": "select * from `business_settings` where (`key` = 'icon') limit 1", "type": "query", "params": [], "bindings": ["icon"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.app", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/app.blade.php", "line": 15}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749057615.3004098, "duration": 0.11892, "duration_str": "119ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.app:15", "source": "view::layouts.admin.app:15", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fapp.blade.php&line=15", "ajax": false, "filename": "app.blade.php", "line": "15"}, "connection": "dease_test", "start_percent": 68.008, "width_percent": 0.98}, {"sql": "select * from `admin_roles` where `admin_roles`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\CentralLogics\\helpers.php", "line": 2196}, {"index": 22, "namespace": "view", "name": "layouts.admin.partials._header", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_header.blade.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749057615.459028, "duration": 0.11631999999999999, "duration_str": "116ms", "memory": 0, "memory_str": null, "filename": "helpers.php:2196", "source": "app/CentralLogics/helpers.php:2196", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FCentralLogics%2Fhelpers.php&line=2196", "ajax": false, "filename": "helpers.php", "line": "2196"}, "connection": "dease_test", "start_percent": 68.989, "width_percent": 0.959}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (4) and `translations`.`translationable_type` = 'App\\Models\\AdminRole' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\AdminRole", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\CentralLogics\\helpers.php", "line": 2196}, {"index": 27, "namespace": "view", "name": "layouts.admin.partials._header", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_header.blade.php", "line": 57}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749057615.5806792, "duration": 0.11775, "duration_str": "118ms", "memory": 0, "memory_str": null, "filename": "helpers.php:2196", "source": "app/CentralLogics/helpers.php:2196", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FCentralLogics%2Fhelpers.php&line=2196", "ajax": false, "filename": "helpers.php", "line": "2196"}, "connection": "dease_test", "start_percent": 69.948, "width_percent": 0.971}, {"sql": "select count(*) as aggregate from `conversations` where (`sender_type` = 'admin' or `receiver_type` = 'admin') and `unread_message_count` > '0'", "type": "query", "params": [], "bindings": ["admin", "admin", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._header", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_header.blade.php", "line": 115}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749057615.732685, "duration": 0.11674, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._header:115", "source": "view::layouts.admin.partials._header:115", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_header.blade.php&line=115", "ajax": false, "filename": "_header.blade.php", "line": "115"}, "connection": "dease_test", "start_percent": 70.919, "width_percent": 0.963}, {"sql": "select * from `business_settings` where `key` = 'system_language' limit 1", "type": "query", "params": [], "bindings": ["system_language"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._header", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_header.blade.php", "line": 125}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749057615.856182, "duration": 0.11622, "duration_str": "116ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._header:125", "source": "view::layouts.admin.partials._header:125", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_header.blade.php&line=125", "ajax": false, "filename": "_header.blade.php", "line": "125"}, "connection": "dease_test", "start_percent": 71.881, "width_percent": 0.958}, {"sql": "select * from `modules` where `modules`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "layouts.admin.partials._header", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_header.blade.php", "line": 159}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749057615.979507, "duration": 0.12015, "duration_str": "120ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._header:159", "source": "view::layouts.admin.partials._header:159", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_header.blade.php&line=159", "ajax": false, "filename": "_header.blade.php", "line": "159"}, "connection": "dease_test", "start_percent": 72.839, "width_percent": 0.991}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (1) and `translations`.`translationable_type` = 'App\\Models\\Module' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\Module", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": "view", "name": "layouts.admin.partials._header", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_header.blade.php", "line": 159}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749057616.1067898, "duration": 0.11875, "duration_str": "119ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._header:159", "source": "view::layouts.admin.partials._header:159", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_header.blade.php&line=159", "ajax": false, "filename": "_header.blade.php", "line": "159"}, "connection": "dease_test", "start_percent": 73.83, "width_percent": 0.979}, {"sql": "select * from `modules` where exists (select * from `zones` inner join `module_zone` on `zones`.`id` = `module_zone`.`zone_id` where `modules`.`id` = `module_zone`.`module_id` and `zone_id` = 2 and `zones`.`id` = 2) and `status` = 1", "type": "query", "params": [], "bindings": ["2", "2", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "layouts.admin.partials._header", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_header.blade.php", "line": 178}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749057617.0887928, "duration": 0.11682, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._header:178", "source": "view::layouts.admin.partials._header:178", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_header.blade.php&line=178", "ajax": false, "filename": "_header.blade.php", "line": "178"}, "connection": "dease_test", "start_percent": 74.809, "width_percent": 0.963}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (1, 2, 5, 7) and `translations`.`translationable_type` = 'App\\Models\\Module' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\Module", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "layouts.admin.partials._header", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_header.blade.php", "line": 178}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749057617.212977, "duration": 0.11753, "duration_str": "118ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._header:178", "source": "view::layouts.admin.partials._header:178", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_header.blade.php&line=178", "ajax": false, "filename": "_header.blade.php", "line": "178"}, "connection": "dease_test", "start_percent": 75.772, "width_percent": 0.969}, {"sql": "select * from `business_settings` where (`key` = 'logo') limit 1", "type": "query", "params": [], "bindings": ["logo"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 6}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749057617.6143398, "duration": 0.11701, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:6", "source": "view::layouts.admin.partials._sidebar_grocery:6", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=6", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "6"}, "connection": "dease_test", "start_percent": 76.741, "width_percent": 0.965}, {"sql": "select count(*) as aggregate from `orders` where (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1 and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["take_away", "delivery", "1", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 97}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749057617.909952, "duration": 0.11778, "duration_str": "118ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:97", "source": "view::layouts.admin.partials._sidebar_grocery:97", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=97", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "97"}, "connection": "dease_test", "start_percent": 77.706, "width_percent": 0.971}, {"sql": "select count(*) as aggregate from `orders` where created_at <> schedule_at and `scheduled` = '1' and (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1 and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["1", "take_away", "delivery", "1", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 110}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749057618.043658, "duration": 0.11694, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:110", "source": "view::layouts.admin.partials._sidebar_grocery:110", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=110", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "110"}, "connection": "dease_test", "start_percent": 78.677, "width_percent": 0.964}, {"sql": "select * from `business_settings` where `key` = 'first_order_status' limit 1", "type": "query", "params": [], "bindings": ["first_order_status"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 116}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749057618.16696, "duration": 0.11652, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:116", "source": "view::layouts.admin.partials._sidebar_grocery:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=116", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "116"}, "connection": "dease_test", "start_percent": 79.641, "width_percent": 0.961}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'initial' and ((created_at <> schedule_at and (`schedule_at` between '2025-06-04 18:20:18' and '2025-06-04 18:50:18') or `schedule_at` < '2025-06-04 18:20:18') or created_at = schedule_at) and (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1 and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["initial", "2025-06-04 18:20:18", "2025-06-04 18:50:18", "2025-06-04 18:20:18", "take_away", "delivery", "1", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 126}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749057618.298689, "duration": 0.1159, "duration_str": "116ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:126", "source": "view::layouts.admin.partials._sidebar_grocery:126", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=126", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "126"}, "connection": "dease_test", "start_percent": 80.602, "width_percent": 0.956}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'pending' and ((created_at <> schedule_at and (`schedule_at` between '2025-06-04 18:20:18' and '2025-06-04 18:50:18') or `schedule_at` < '2025-06-04 18:20:18') or created_at = schedule_at) and (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1 and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["pending", "2025-06-04 18:20:18", "2025-06-04 18:50:18", "2025-06-04 18:20:18", "take_away", "delivery", "1", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 140}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749057618.4301, "duration": 0.11944, "duration_str": "119ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:140", "source": "view::layouts.admin.partials._sidebar_grocery:140", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=140", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "140"}, "connection": "dease_test", "start_percent": 81.558, "width_percent": 0.985}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'accepted' and ((created_at <> schedule_at and (`schedule_at` between '2025-06-04 18:20:18' and '2025-06-04 18:50:18') or `schedule_at` < '2025-06-04 18:20:18') or created_at = schedule_at) and (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1 and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["accepted", "2025-06-04 18:20:18", "2025-06-04 18:50:18", "2025-06-04 18:20:18", "take_away", "delivery", "1", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 154}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749057618.5639172, "duration": 0.11862, "duration_str": "119ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:154", "source": "view::layouts.admin.partials._sidebar_grocery:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=154", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "154"}, "connection": "dease_test", "start_percent": 82.542, "width_percent": 0.978}, {"sql": "select count(*) as aggregate from `orders` where `order_status` in ('confirmed', 'processing', 'handover') and ((created_at <> schedule_at and (`schedule_at` between '2025-06-04 18:20:18' and '2025-06-04 18:50:18') or `schedule_at` < '2025-06-04 18:20:18') or created_at = schedule_at) and (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1 and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["confirmed", "processing", "handover", "2025-06-04 18:20:18", "2025-06-04 18:50:18", "2025-06-04 18:20:18", "take_away", "delivery", "1", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 167}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749057618.696851, "duration": 0.11663, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:167", "source": "view::layouts.admin.partials._sidebar_grocery:167", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=167", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "167"}, "connection": "dease_test", "start_percent": 83.52, "width_percent": 0.962}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'picked_up' and ((created_at <> schedule_at and (`schedule_at` between '2025-06-04 18:20:18' and '2025-06-04 18:50:18') or `schedule_at` < '2025-06-04 18:20:18') or created_at = schedule_at) and (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1 and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["picked_up", "2025-06-04 18:20:18", "2025-06-04 18:50:18", "2025-06-04 18:20:18", "take_away", "delivery", "1", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749057618.8292148, "duration": 0.11821, "duration_str": "118ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:180", "source": "view::layouts.admin.partials._sidebar_grocery:180", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=180", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "180"}, "connection": "dease_test", "start_percent": 84.482, "width_percent": 0.975}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'delivered' and (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1 and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["delivered", "take_away", "delivery", "1", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 193}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749057618.961735, "duration": 0.11784, "duration_str": "118ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:193", "source": "view::layouts.admin.partials._sidebar_grocery:193", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=193", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "193"}, "connection": "dease_test", "start_percent": 85.457, "width_percent": 0.972}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'canceled' and (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1 and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["canceled", "take_away", "delivery", "1", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 206}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749057619.0955381, "duration": 0.11884, "duration_str": "119ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:206", "source": "view::layouts.admin.partials._sidebar_grocery:206", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=206", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "206"}, "connection": "dease_test", "start_percent": 86.428, "width_percent": 0.98}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'failed' and (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1 and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["failed", "take_away", "delivery", "1", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 219}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749057619.2288291, "duration": 0.11741, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:219", "source": "view::layouts.admin.partials._sidebar_grocery:219", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=219", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "219"}, "connection": "dease_test", "start_percent": 87.408, "width_percent": 0.968}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'refunded' and (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1 and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["refunded", "take_away", "delivery", "1", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 232}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749057619.360789, "duration": 0.11653, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:232", "source": "view::layouts.admin.partials._sidebar_grocery:232", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=232", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "232"}, "connection": "dease_test", "start_percent": 88.376, "width_percent": 0.961}, {"sql": "select count(*) as aggregate from `orders` where exists (select * from `offline_payments` where `orders`.`id` = `offline_payments`.`order_id`) and (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1 and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["take_away", "delivery", "1", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 246}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749057619.4983509, "duration": 0.11818000000000001, "duration_str": "118ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:246", "source": "view::layouts.admin.partials._sidebar_grocery:246", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=246", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "246"}, "connection": "dease_test", "start_percent": 89.337, "width_percent": 0.974}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'refund_requested' and (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1 and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["refund_requested", "take_away", "delivery", "1", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 279}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749057619.639241, "duration": 0.11746, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:279", "source": "view::layouts.admin.partials._sidebar_grocery:279", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=279", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "279"}, "connection": "dease_test", "start_percent": 90.311, "width_percent": 0.968}, {"sql": "select * from `business_settings` where `key` = 'product_approval' limit 1", "type": "query", "params": [], "bindings": ["product_approval"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\CentralLogics\\helpers.php", "line": 3303}, {"index": 17, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 482}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749057619.92898, "duration": 0.11709, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "helpers.php:3303", "source": "app/CentralLogics/helpers.php:3303", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FCentralLogics%2Fhelpers.php&line=3303", "ajax": false, "filename": "helpers.php", "line": "3303"}, "connection": "dease_test", "start_percent": 91.28, "width_percent": 0.965}, {"sql": "select * from `stores` where exists (select * from `vendors` where `stores`.`vendor_id` = `vendors`.`id` and `status` is null) and `module_id` = 1 and `zone_id` = 2", "type": "query", "params": [], "bindings": ["1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 529}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749057620.103081, "duration": 0.11701, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:529", "source": "view::layouts.admin.partials._sidebar_grocery:529", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=529", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "529"}, "connection": "dease_test", "start_percent": 92.245, "width_percent": 0.965}, {"sql": "select * from `business_settings` where (`key` = 'business_name') limit 1", "type": "query", "params": [], "bindings": ["business_name"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._footer", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_footer.blade.php", "line": 5}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749057620.558608, "duration": 0.11782, "duration_str": "118ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._footer:5", "source": "view::layouts.admin.partials._footer:5", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_footer.blade.php&line=5", "ajax": false, "filename": "_footer.blade.php", "line": "5"}, "connection": "dease_test", "start_percent": 93.21, "width_percent": 0.971}, {"sql": "select * from `business_settings` where (`key` = 'footer_text') limit 1", "type": "query", "params": [], "bindings": ["footer_text"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._footer", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_footer.blade.php", "line": 6}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749057620.681952, "duration": 0.11655, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._footer:6", "source": "view::layouts.admin.partials._footer:6", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_footer.blade.php&line=6", "ajax": false, "filename": "_footer.blade.php", "line": "6"}, "connection": "dease_test", "start_percent": 94.181, "width_percent": 0.961}, {"sql": "select * from `modules` where `status` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "layouts.admin.app", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/app.blade.php", "line": 257}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749057620.847126, "duration": 0.1162, "duration_str": "116ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.app:257", "source": "view::layouts.admin.app:257", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fapp.blade.php&line=257", "ajax": false, "filename": "app.blade.php", "line": "257"}, "connection": "dease_test", "start_percent": 95.142, "width_percent": 0.958}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (1, 2, 5, 7) and `translations`.`translationable_type` = 'App\\Models\\Module' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\Module", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "layouts.admin.app", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/app.blade.php", "line": 257}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749057620.969301, "duration": 0.11917, "duration_str": "119ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.app:257", "source": "view::layouts.admin.app:257", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fapp.blade.php&line=257", "ajax": false, "filename": "app.blade.php", "line": "257"}, "connection": "dease_test", "start_percent": 96.1, "width_percent": 0.983}, {"sql": "select * from `business_settings` where `key` = 'fcm_credentials' limit 1", "type": "query", "params": [], "bindings": null, "hints": null, "show_copy": false, "backtrace": [], "start": 1749057621.166647, "duration": 0.11661, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "", "source": "", "xdebug_link": null, "connection": "dease_test", "start_percent": 97.083, "width_percent": 0.961}, {"sql": "select * from `business_settings` where `key` = 'order_notification_type' limit 1", "type": "query", "params": [], "bindings": null, "hints": null, "show_copy": false, "backtrace": [], "start": 1749057621.284226, "duration": 0.11962, "duration_str": "120ms", "memory": 0, "memory_str": null, "filename": "", "source": "", "xdebug_link": null, "connection": "dease_test", "start_percent": 98.044, "width_percent": 0.986}, {"sql": "select * from `business_settings` where `key` = 'admin_order_notification' limit 1", "type": "query", "params": [], "bindings": null, "hints": null, "show_copy": false, "backtrace": [], "start": 1749057621.404611, "duration": 0.11758, "duration_str": "118ms", "memory": 0, "memory_str": null, "filename": "", "source": "", "xdebug_link": null, "connection": "dease_test", "start_percent": 99.031, "width_percent": 0.969}]}, "models": {"data": {"App\\Models\\Translation": {"value": 43, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FTranslation.php&line=1", "ajax": false, "filename": "Translation.php", "line": "?"}}, "App\\Models\\User": {"value": 30, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\BusinessSetting": {"value": 13, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FBusinessSetting.php&line=1", "ajax": false, "filename": "BusinessSetting.php", "line": "?"}}, "App\\Models\\Module": {"value": 13, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FModule.php&line=1", "ajax": false, "filename": "Module.php", "line": "?"}}, "App\\Models\\DeliveryMan": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FDeliveryMan.php&line=1", "ajax": false, "filename": "DeliveryMan.php", "line": "?"}}, "App\\Models\\Item": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FItem.php&line=1", "ajax": false, "filename": "Item.php", "line": "?"}}, "App\\Models\\Store": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}, "App\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "App\\Models\\AdminRole": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FAdminRole.php&line=1", "ajax": false, "filename": "AdminRole.php", "line": "?"}}}, "count": 115, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0pqieAzfH74oJerE5OhbnxrPyCObJe7jMhZxcftT", "site_direction": "ltr", "_previous": "array:1 [\n  \"url\" => \"http://localhost/Admin-panel/admin\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "landing_site_direction": "ltr", "six_captcha": "<PERSON><PERSON><PERSON>", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "5", "dash_params": "array:6 [\n  \"zone_id\" => \"all\"\n  \"module_id\" => 1\n  \"statistics_type\" => \"overall\"\n  \"user_overview\" => \"overall\"\n  \"commission_overview\" => \"this_year\"\n  \"business_overview\" => \"overall\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/admin", "status_code": "<pre class=sf-dump id=sf-dump-346028955 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-346028955\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1037561771 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1037561771\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1472032331 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1472032331\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1795750208 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"49 characters\">http://localhost/Admin-panel/login/admin-employee</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">en-US,en;q=0.9,fr-DZ;q=0.8,fr;q=0.7,ar-DZ;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1274 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ik5jRlo5KzllMDgvcERoQklrT3VUYkE9PSIsInZhbHVlIjoiVVFoUkF1aUFCQ1pIQjcxVWRKN0Y0bnJabkgrWFEwckhLVmF1NnQ3YkpPblJZVmhCL05ZK2h4c3Q3VGZVeWErRkNGeDJDY0RFMCt6NnM1d1E4cVlxRUpKN3lCUVFheWxUaXlCVWdpb21pRThQbmRyU3E0MEpvQlVoYVo3a25SeDNnaGlZR3B6YTJFc3J0KytQRlpHSE1CakpzbGR2OXRPeTN6ZmNUWmI4N2xnUlZCRW1LeHlPWElHcmthb3NkSnJ0R2NqUlUwTWZseW1zVzgrcTg3WEpzajFpWGdUMFZQMGd0eGN6ZlFDUWFBbz0iLCJtYWMiOiI1MjUzMzQ0OGQ1NTE1NTBkNzI5ZjQwZWE4OTFiNDJiNTM2ZjBiYjg4NTkxNDBhZjI4MjEwZmIwMzg3NzQ2ZWNhIiwidGFnIjoiIn0%3D; SL_G_WPT_TO=fr; XSRF-TOKEN=eyJpdiI6IjJlNmdDd2daTjVRRU02bmpQYldiZmc9PSIsInZhbHVlIjoic1hBMmh4YjBHRUduZnZtMnBpT0ZlUktuSVlUUUVyWXpQYWZiQVVHOE4zYmQ1Tk1xdFZ2Y2xvbTJIdDRWSHBZWGlLd2g0eVhpU1VCc3Q3bXNWcXdPSW9MaUdEVkw4a2Nya3hWUGVnRkVRVkk1OVdFUVhhdHRxVHY1K0NYOWJVak8iLCJtYWMiOiIzODBjYzMzZjNhN2NiODc1YWViMDJkOTliM2MxMWFlMTE4NjFlZjc4YzA2ZjNkNzhmYTY0MjI4M2Q0OWI4MzIwIiwidGFnIjoiIn0%3D; deaseapp_session=eyJpdiI6Img1UUFIR3JVME5ocUw1Qk1WS282dHc9PSIsInZhbHVlIjoiTXNxU2hhUlVNOE1DSGRJT09ONEd5Mkcrd3ZNV0JKMTVkY2M3MXdZVnhUMWY0Q0dGNklnZzQ1RVZIUDRkVEJudmQ0WGhnUWR5bSs2M1dIUHBkN0thQmFpenZqbHR3MlhMSTd2aFlzb0RmY2Q3Q0xuOTFyd2tCTWJwQ1V4dUlseDEiLCJtYWMiOiIxZjY0YTAzZmYxNDZiMTFhMjQyMjRhNjc1Zjg1NjkyZTBjNjA2MjUyNWZkZmRiNDgxYThjN2M3ODI5ZmE1ZGJjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1795750208\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-211771349 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>SL_G_WPT_TO</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0pqieAzfH74oJerE5OhbnxrPyCObJe7jMhZxcftT</span>\"\n  \"<span class=sf-dump-key>deaseapp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">V9hQnNfbej1xZs6As7bY8YDI4v6i5lCcrxfyz96y</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-211771349\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-226409551 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 04 Jun 2025 17:20:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"414 characters\">XSRF-TOKEN=eyJpdiI6IktXWW9DZWp0NkdKenlPWlkza3BqQXc9PSIsInZhbHVlIjoiQU9qMHR3SmRFMWxhSnhaS1YzaGJHNDFseVN5bkp2Z0RCeWEvMzVlMmhDOHFTZCtrck8xbzZNYUgxSzFidWM0RXRrR3pIeTB2M3RSRERzdGxyMFBTdjBxZjBUNTRGalpMUm1FcVlFR29SNkt5dHNmOWhGcUNpa1JRSVArZ3A0Q1MiLCJtYWMiOiI3MGUxNzlkNGQ4YWM1YzE2N2YyZjdlZDdmZWU4N2VhNTliODZiZTE0MDY5MDM2MDhkZjBmYWE4ZDAxZDRhY2I4IiwidGFnIjoiIn0%3D; expires=Wed, 04 Jun 2025 19:20:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"430 characters\">deaseapp_session=eyJpdiI6IllOTHZNRlQ0WXFmcTcxK0FUZGpKNUE9PSIsInZhbHVlIjoiUWhvZktEU0QzMGxsNGU3NU4wSUQ2MnJlRjBlQWxiY3I1QjNkMzNDZUthcFpncVFmT0QwRlF1VzBFUVRUWnR4aUVLMi81UDdtNE9OOXczdi9Db3ZhYkU0L0VWVVZEQnROeHo3QjROVEltS290SjVBVzBWQ2QrTEZpNVpmb04ramMiLCJtYWMiOiIzNGRhMDk1Zjc0OGIxMTNmNmNiMzVmYjgwZTg2MjdkMjRmZDBhOTg2Nzc2ZDcxMWI5OTBlZWFkYmM3YjdiNDI4IiwidGFnIjoiIn0%3D; expires=Wed, 04 Jun 2025 19:20:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IktXWW9DZWp0NkdKenlPWlkza3BqQXc9PSIsInZhbHVlIjoiQU9qMHR3SmRFMWxhSnhaS1YzaGJHNDFseVN5bkp2Z0RCeWEvMzVlMmhDOHFTZCtrck8xbzZNYUgxSzFidWM0RXRrR3pIeTB2M3RSRERzdGxyMFBTdjBxZjBUNTRGalpMUm1FcVlFR29SNkt5dHNmOWhGcUNpa1JRSVArZ3A0Q1MiLCJtYWMiOiI3MGUxNzlkNGQ4YWM1YzE2N2YyZjdlZDdmZWU4N2VhNTliODZiZTE0MDY5MDM2MDhkZjBmYWE4ZDAxZDRhY2I4IiwidGFnIjoiIn0%3D; expires=Wed, 04-Jun-2025 19:20:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">deaseapp_session=eyJpdiI6IllOTHZNRlQ0WXFmcTcxK0FUZGpKNUE9PSIsInZhbHVlIjoiUWhvZktEU0QzMGxsNGU3NU4wSUQ2MnJlRjBlQWxiY3I1QjNkMzNDZUthcFpncVFmT0QwRlF1VzBFUVRUWnR4aUVLMi81UDdtNE9OOXczdi9Db3ZhYkU0L0VWVVZEQnROeHo3QjROVEltS290SjVBVzBWQ2QrTEZpNVpmb04ramMiLCJtYWMiOiIzNGRhMDk1Zjc0OGIxMTNmNmNiMzVmYjgwZTg2MjdkMjRmZDBhOTg2Nzc2ZDcxMWI5OTBlZWFkYmM3YjdiNDI4IiwidGFnIjoiIn0%3D; expires=Wed, 04-Jun-2025 19:20:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-226409551\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1400742448 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0pqieAzfH74oJerE5OhbnxrPyCObJe7jMhZxcftT</span>\"\n  \"<span class=sf-dump-key>site_direction</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ltr</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/Admin-panel/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>landing_site_direction</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ltr</span>\"\n  \"<span class=sf-dump-key>six_captcha</span>\" => \"<span class=sf-dump-str title=\"5 characters\">CyKOr</span>\"\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>dash_params</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>zone_id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">all</span>\"\n    \"<span class=sf-dump-key>module_id</span>\" => <span class=sf-dump-num>1</span>\n    \"<span class=sf-dump-key>statistics_type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">overall</span>\"\n    \"<span class=sf-dump-key>user_overview</span>\" => \"<span class=sf-dump-str title=\"7 characters\">overall</span>\"\n    \"<span class=sf-dump-key>commission_overview</span>\" => \"<span class=sf-dump-str title=\"9 characters\">this_year</span>\"\n    \"<span class=sf-dump-key>business_overview</span>\" => \"<span class=sf-dump-str title=\"7 characters\">overall</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1400742448\", {\"maxDepth\":0})</script>\n"}}