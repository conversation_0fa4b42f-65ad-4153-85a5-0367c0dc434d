<?php

// Test if autoload files are working
echo "Testing autoload files...\n";

try {
    require __DIR__.'/vendor/autoload.php';
    echo "Vendor autoload successful\n";
} catch (Exception $e) {
    echo "Vendor autoload failed: " . $e->getMessage() . "\n";
    exit(1);
}

try {
    $app = require_once __DIR__.'/bootstrap/app.php';
    echo "Bootstrap app successful\n";
} catch (Exception $e) {
    echo "Bootstrap app failed: " . $e->getMessage() . "\n";
    exit(1);
}

echo "All autoload tests passed!\n";
