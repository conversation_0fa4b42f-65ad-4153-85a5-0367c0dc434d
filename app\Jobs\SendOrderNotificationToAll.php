<?php

namespace App\Jobs;

use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use App\Models\Order;
use App\Models\DeliveryMan;
use Illuminate\Support\Facades\DB;
use App\CentralLogics\Helpers;


class SendOrderNotificationToAll implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    protected int $orderId;
    protected array $arrayOfAllDeliverymen;
    protected array $arrayOfFilteredDeliverymen;
    protected array $busyDmFilteredOfOne;
    protected array $busyDmFilteredOfMultiple;
    protected int $delay_notification;
    protected int $currentTokenIndex;

    //protected $delay = 30;
    public function __construct($orderId, $arrayOfAllDeliverymen, $delay_notification, $busyDmFilteredOfOne=[], $busyDmFilteredOfMultiple=[], $arrayOfFilteredDeliverymen=[], $currentTokenIndex = 0)
    {
        //
        $this->orderId = $orderId;
        $this->arrayOfAllDeliverymen = $arrayOfAllDeliverymen;
        $this->arrayOfFilteredDeliverymen = $arrayOfFilteredDeliverymen;
        $this->busyDmFilteredOfOne = $busyDmFilteredOfOne;
        $this->busyDmFilteredOfMultiple = $busyDmFilteredOfMultiple;
        $this->delay_notification = $delay_notification;
        $this->currentTokenIndex = $currentTokenIndex;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('Run Queue processing of sending order to rest deliverymen started.');
        try {
            $order = Order::find($this->orderId);
        } catch (Exception $e) {
            Log::info($e->getMessage());
            return;
        }
        if (($order->order_type == 'delivery' && in_array($order->order_status, ['processing', 'handover'])) || (in_array($order->order_status, ['pending', 'confirmed']) && $order->order_type == 'parcel') && !isset($order->delivery_man_id)) { //|| ($order->order_status == 'pending' && $order->order_type == 'parcel')
            $dm = DeliveryMan::find($this->arrayOfAllDeliverymen[$this->currentTokenIndex]['id']);
            if ($dm->active == 1 && $dm->current_orders <= 2) {
                $this->notify_deliverman($this->orderId, $dm->id, $dm->fcm_token);
            }
            $nextIndex = $this->currentTokenIndex + 1;
            if (!empty($this->busyDmFilteredOfOne) && $nextIndex >= count($this->arrayOfAllDeliverymen)) {
                try {
                    SendMatchedOrderNotification::dispatch($this->orderId, $this->arrayOfAllDeliverymen, $this->delay_notification, $this->busyDmFilteredOfOne, $this->busyDmFilteredOfMultiple, $this->arrayOfFilteredDeliverymen, $nextIndex = 0)->afterCommit()->delay(Carbon::now()->addSeconds($this->delay_notification));
                } catch (Exception $e) {
                    Log::info('Error dispatching job: ' . $e->getMessage());
                }
            } else if (!empty($this->busyDmFilteredOfMultiple) && $nextIndex >= count($this->arrayOfAllDeliverymen)) {
                try {
                    SendMatchedForTwoOrdersNotification::dispatch($this->orderId, $this->arrayOfAllDeliverymen, $this->delay_notification, $this->busyDmFilteredOfOne, $this->busyDmFilteredOfMultiple, $this->arrayOfFilteredDeliverymen, $nextIndex = 0)->afterCommit()->delay(Carbon::now()->addSeconds($this->delay_notification));
                } catch (Exception $e) {
                    Log::info('Error dispatching job: ' . $e->getMessage());
                }
            } elseif (!empty($this->arrayOfFilteredDeliverymen) && $nextIndex >= count($this->arrayOfAllDeliverymen)) {
                try {
                    SendOrderNotification::dispatch($this->orderId, $this->arrayOfAllDeliverymen, $this->delay_notification, $this->busyDmFilteredOfOne, $this->busyDmFilteredOfMultiple, $this->arrayOfFilteredDeliverymen, $nextIndex = 0)->afterCommit()->delay(Carbon::now()->addSeconds($this->delay_notification));
                } catch (Exception $e) {
                    Log::info('Error dispatching job: ' . $e->getMessage());
                }
                //problem here
            } else {
                if ($nextIndex >= count($this->arrayOfAllDeliverymen) && empty($this->busyDmFilteredOfMultiple) && empty($this->busyDmFilteredOfOne) && empty($this->arrayOfFilteredDeliverymen)) {
                    $nextIndex = 0;
                }
                try {
                    SendOrderNotificationToAll::dispatch($this->orderId, $this->arrayOfAllDeliverymen, $this->delay_notification, $this->busyDmFilteredOfOne, $this->busyDmFilteredOfMultiple, $this->arrayOfFilteredDeliverymen, $nextIndex)->afterCommit()->delay(Carbon::now()->addSeconds($this->delay_notification));
                } catch (Exception $e) {
                    Log::info('Error dispatching job: ' . $e->getMessage());
                }
            }
        }
    }

    public function notify_deliverman($orderId, $dmID, $dmFcmToken): void
    {
        DB::beginTransaction();
        try {
            
            $order = Order::find($orderId);

            // Check if order is still unassigned
            if ($order->delivery_man_id !== null) {

                return;
            }

            $order->is_showing = $dmID;
            $order->save();
            DB::commit();
            $data = [
                'title' => translate('messages.order_push_title'),
                'description' => translate('messages.new_order_push_description'),
                'order_id' => $order->id,
                'image' => '',
                'module_id' => $order->module_id,
                'order_type' => $order->order_type,
                'zone_id' => $order->zone_id,
                'type' => 'new_order',
            ];
            Helpers::send_push_notif_to_device($dmFcmToken, $data);
            //Helpers::send_push_notif_to_device($dmFcmToken, $data);
        } catch (Exception $e) {
            DB::rollback();
            Log::info($e->getMessage());
            return;
        }
    }
}
