{"__meta": {"id": "X37c0cd6468e79dcad839b762ac224310", "datetime": "2025-06-04 18:20:03", "utime": **********.693463, "method": "POST", "uri": "/Admin-panel/login_submit", "ip": "::1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749057599.63286, "end": **********.693504, "duration": 4.060644149780273, "duration_str": "4.06s", "measures": [{"label": "Booting", "start": 1749057599.63286, "relative_start": 0, "end": **********.542178, "relative_end": **********.542178, "duration": 2.909317970275879, "duration_str": "2.91s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.542209, "relative_start": 2.909348964691162, "end": **********.69351, "relative_end": 5.9604644775390625e-06, "duration": 1.1513011455535889, "duration_str": "1.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 31559352, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST login_submit", "middleware": "web, actch, guest:admin,vendor", "controller": "App\\Http\\Controllers\\LoginController@submit", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "login_post", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FLoginController.php&line=121\" onclick=\"\">app/Http/Controllers/LoginController.php:121-202</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.87186, "accumulated_duration_str": "872ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `business_settings` where `key` = 'system_language' limit 1", "type": "query", "params": [], "bindings": ["system_language"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "middleware", "name": "localization", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\Localization.php", "line": 24}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 78}], "start": **********.6003969, "duration": 0.12522, "duration_str": "125ms", "memory": 0, "memory_str": null, "filename": "localization:24", "source": "middleware::localization:24", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FMiddleware%2FLocalization.php&line=24", "ajax": false, "filename": "Localization.php", "line": "24"}, "connection": "dease_test", "start_percent": 0, "width_percent": 14.362}, {"sql": "select * from `business_settings` where `key` = 'recaptcha' limit 1", "type": "query", "params": [], "bindings": ["recaptcha"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\CentralLogics\\helpers.php", "line": 938}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/LoginController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\LoginController.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.7892432, "duration": 0.*****************, "duration_str": "124ms", "memory": 0, "memory_str": null, "filename": "helpers.php:938", "source": "app/CentralLogics/helpers.php:938", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FCentralLogics%2Fhelpers.php&line=938", "ajax": false, "filename": "helpers.php", "line": "938"}, "connection": "dease_test", "start_percent": 14.362, "width_percent": 14.166}, {"sql": "select exists(select * from `admins` where `email` = '<EMAIL>' and `role_id` = 1) as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/LoginController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\LoginController.php", "line": 152}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.927536, "duration": 0.12511, "duration_str": "125ms", "memory": 0, "memory_str": null, "filename": "LoginController.php:152", "source": "app/Http/Controllers/LoginController.php:152", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FLoginController.php&line=152", "ajax": false, "filename": "LoginController.php", "line": "152"}, "connection": "dease_test", "start_percent": 28.529, "width_percent": 14.35}, {"sql": "select * from `admins` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 139}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 381}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/LoginController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\LoginController.php", "line": 96}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/LoginController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\LoginController.php", "line": 177}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.060985, "duration": 0.1237, "duration_str": "124ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:139", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:139", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=139", "ajax": false, "filename": "EloquentUserProvider.php", "line": "139"}, "connection": "dease_test", "start_percent": 42.878, "width_percent": 14.188}, {"sql": "select * from `admins` where `admins`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/LoginController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\LoginController.php", "line": 180}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.26682, "duration": 0.12481, "duration_str": "125ms", "memory": 0, "memory_str": null, "filename": "LoginController.php:180", "source": "app/Http/Controllers/LoginController.php:180", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FLoginController.php&line=180", "ajax": false, "filename": "LoginController.php", "line": "180"}, "connection": "dease_test", "start_percent": 57.067, "width_percent": 14.315}, {"sql": "select * from `modules` where `status` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/LoginController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\LoginController.php", "line": 183}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4031348, "duration": 0.12453, "duration_str": "125ms", "memory": 0, "memory_str": null, "filename": "LoginController.php:183", "source": "app/Http/Controllers/LoginController.php:183", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FLoginController.php&line=183", "ajax": false, "filename": "LoginController.php", "line": "183"}, "connection": "dease_test", "start_percent": 71.382, "width_percent": 14.283}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (1, 2, 5, 7) and `translations`.`translationable_type` = 'App\\Models\\Module' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\Module", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/LoginController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\LoginController.php", "line": 183}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.53979, "duration": 0.*****************, "duration_str": "125ms", "memory": 0, "memory_str": null, "filename": "LoginController.php:183", "source": "app/Http/Controllers/LoginController.php:183", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FLoginController.php&line=183", "ajax": false, "filename": "LoginController.php", "line": "183"}, "connection": "dease_test", "start_percent": 85.665, "width_percent": 14.335}]}, "models": {"data": {"App\\Models\\Translation": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FTranslation.php&line=1", "ajax": false, "filename": "Translation.php", "line": "?"}}, "App\\Models\\Module": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FModule.php&line=1", "ajax": false, "filename": "Module.php", "line": "?"}}, "App\\Models\\BusinessSetting": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FBusinessSetting.php&line=1", "ajax": false, "filename": "BusinessSetting.php", "line": "?"}}, "App\\Models\\Admin": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}}, "count": 16, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0pqieAzfH74oJerE5OhbnxrPyCObJe7jMhZxcftT", "site_direction": "ltr", "_previous": "array:1 [\n  \"url\" => \"http://localhost/Admin-panel/login/admin-employee\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "landing_site_direction": "ltr", "six_captcha": "<PERSON><PERSON><PERSON>", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "5", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login_submit", "status_code": "<pre class=sf-dump id=sf-dump-1116796919 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1116796919\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-457803222 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-457803222\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1631383644 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0pqieAzfH74oJerE5OhbnxrPyCObJe7jMhZxcftT</span>\"\n  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"14 characters\">admin_employee</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"16 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>custome_recaptcha</span>\" => \"<span class=sf-dump-str title=\"5 characters\">CyKOr</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1631383644\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">138</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"49 characters\">http://localhost/Admin-panel/login/admin-employee</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">en-US,en;q=0.9,fr-DZ;q=0.8,fr;q=0.7,ar-DZ;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1274 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ik5jRlo5KzllMDgvcERoQklrT3VUYkE9PSIsInZhbHVlIjoiVVFoUkF1aUFCQ1pIQjcxVWRKN0Y0bnJabkgrWFEwckhLVmF1NnQ3YkpPblJZVmhCL05ZK2h4c3Q3VGZVeWErRkNGeDJDY0RFMCt6NnM1d1E4cVlxRUpKN3lCUVFheWxUaXlCVWdpb21pRThQbmRyU3E0MEpvQlVoYVo3a25SeDNnaGlZR3B6YTJFc3J0KytQRlpHSE1CakpzbGR2OXRPeTN6ZmNUWmI4N2xnUlZCRW1LeHlPWElHcmthb3NkSnJ0R2NqUlUwTWZseW1zVzgrcTg3WEpzajFpWGdUMFZQMGd0eGN6ZlFDUWFBbz0iLCJtYWMiOiI1MjUzMzQ0OGQ1NTE1NTBkNzI5ZjQwZWE4OTFiNDJiNTM2ZjBiYjg4NTkxNDBhZjI4MjEwZmIwMzg3NzQ2ZWNhIiwidGFnIjoiIn0%3D; SL_G_WPT_TO=fr; XSRF-TOKEN=eyJpdiI6IlNETlBzMUdsVWVWeVhoOXhFQVJ1aGc9PSIsInZhbHVlIjoiS3dOM1BIdlZzazB3eGhVbk56M0Zaak81cFRTd1Q4MmJab0lIelNtdkF6cFovQmg0YWIydzI0ZDZNaHhMZDJRdmJWY3RQSStDTjBuUGRXdjFtdGNLOUhyOHZNMk1FL3dsZUFmQTVQS0ppbk5XbVYzeEF0NDdGUkxQaDhJL0U3aDciLCJtYWMiOiIwYWNhODBlY2MwZGQ2YTAyNDhhMjcwNmM0Mzc3NmViNDRhZTgxZTQxZWU1NjgwZmU0YzgyY2FiMzIyYWVhMmQ2IiwidGFnIjoiIn0%3D; deaseapp_session=eyJpdiI6IjVkZTl0alEya0pzT3dqV1RURVdsVHc9PSIsInZhbHVlIjoiOXIwUHpacCt6RmROM0xhSjMwRmRTNWFLS3A4Tmx6WGI4S0JTaXdSL0RIbzhVRHVIbmdGbklCYkhWL2h0U2J4YTRnYjJFeFFXRzBaM3pwejRRdGpBejBzam1UZ21uM1NaczlDaEs0cDB6bFJVUUt6MDhPaGpKS3dRZEtJdGVpd3YiLCJtYWMiOiJkMWQ4NTBlMTFmOWE0YWQ2Mzg1MTFkNzRjNDM1ZGE3NTgxZGQ4N2Q5MDRiZWM2MDllM2U2ODQ3MDg4YTg1MzZiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>SL_G_WPT_TO</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0pqieAzfH74oJerE5OhbnxrPyCObJe7jMhZxcftT</span>\"\n  \"<span class=sf-dump-key>deaseapp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gpQ1MFhnTb7GUUw0qOqWENWCCbirwFJdZJaLqpae</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-143641997 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 04 Jun 2025 17:20:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/Admin-panel/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"414 characters\">XSRF-TOKEN=eyJpdiI6IjJlNmdDd2daTjVRRU02bmpQYldiZmc9PSIsInZhbHVlIjoic1hBMmh4YjBHRUduZnZtMnBpT0ZlUktuSVlUUUVyWXpQYWZiQVVHOE4zYmQ1Tk1xdFZ2Y2xvbTJIdDRWSHBZWGlLd2g0eVhpU1VCc3Q3bXNWcXdPSW9MaUdEVkw4a2Nya3hWUGVnRkVRVkk1OVdFUVhhdHRxVHY1K0NYOWJVak8iLCJtYWMiOiIzODBjYzMzZjNhN2NiODc1YWViMDJkOTliM2MxMWFlMTE4NjFlZjc4YzA2ZjNkNzhmYTY0MjI4M2Q0OWI4MzIwIiwidGFnIjoiIn0%3D; expires=Wed, 04 Jun 2025 19:20:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"430 characters\">deaseapp_session=eyJpdiI6Img1UUFIR3JVME5ocUw1Qk1WS282dHc9PSIsInZhbHVlIjoiTXNxU2hhUlVNOE1DSGRJT09ONEd5Mkcrd3ZNV0JKMTVkY2M3MXdZVnhUMWY0Q0dGNklnZzQ1RVZIUDRkVEJudmQ0WGhnUWR5bSs2M1dIUHBkN0thQmFpenZqbHR3MlhMSTd2aFlzb0RmY2Q3Q0xuOTFyd2tCTWJwQ1V4dUlseDEiLCJtYWMiOiIxZjY0YTAzZmYxNDZiMTFhMjQyMjRhNjc1Zjg1NjkyZTBjNjA2MjUyNWZkZmRiNDgxYThjN2M3ODI5ZmE1ZGJjIiwidGFnIjoiIn0%3D; expires=Wed, 04 Jun 2025 19:20:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjJlNmdDd2daTjVRRU02bmpQYldiZmc9PSIsInZhbHVlIjoic1hBMmh4YjBHRUduZnZtMnBpT0ZlUktuSVlUUUVyWXpQYWZiQVVHOE4zYmQ1Tk1xdFZ2Y2xvbTJIdDRWSHBZWGlLd2g0eVhpU1VCc3Q3bXNWcXdPSW9MaUdEVkw4a2Nya3hWUGVnRkVRVkk1OVdFUVhhdHRxVHY1K0NYOWJVak8iLCJtYWMiOiIzODBjYzMzZjNhN2NiODc1YWViMDJkOTliM2MxMWFlMTE4NjFlZjc4YzA2ZjNkNzhmYTY0MjI4M2Q0OWI4MzIwIiwidGFnIjoiIn0%3D; expires=Wed, 04-Jun-2025 19:20:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">deaseapp_session=eyJpdiI6Img1UUFIR3JVME5ocUw1Qk1WS282dHc9PSIsInZhbHVlIjoiTXNxU2hhUlVNOE1DSGRJT09ONEd5Mkcrd3ZNV0JKMTVkY2M3MXdZVnhUMWY0Q0dGNklnZzQ1RVZIUDRkVEJudmQ0WGhnUWR5bSs2M1dIUHBkN0thQmFpenZqbHR3MlhMSTd2aFlzb0RmY2Q3Q0xuOTFyd2tCTWJwQ1V4dUlseDEiLCJtYWMiOiIxZjY0YTAzZmYxNDZiMTFhMjQyMjRhNjc1Zjg1NjkyZTBjNjA2MjUyNWZkZmRiNDgxYThjN2M3ODI5ZmE1ZGJjIiwidGFnIjoiIn0%3D; expires=Wed, 04-Jun-2025 19:20:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-143641997\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-646334316 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0pqieAzfH74oJerE5OhbnxrPyCObJe7jMhZxcftT</span>\"\n  \"<span class=sf-dump-key>site_direction</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ltr</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"49 characters\">http://localhost/Admin-panel/login/admin-employee</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>landing_site_direction</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ltr</span>\"\n  \"<span class=sf-dump-key>six_captcha</span>\" => \"<span class=sf-dump-str title=\"5 characters\">CyKOr</span>\"\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-646334316\", {\"maxDepth\":0})</script>\n"}}