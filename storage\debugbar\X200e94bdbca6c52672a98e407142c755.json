{"__meta": {"id": "X200e94bdbca6c52672a98e407142c755", "datetime": "2025-06-04 15:32:03", "utime": 1749047523.654402, "method": "GET", "uri": "/Admin-panel/admin", "ip": "::1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749047507.778422, "end": 1749047523.654423, "duration": 15.876000881195068, "duration_str": "15.88s", "measures": [{"label": "Booting", "start": 1749047507.778422, "relative_start": 0, "end": **********.084245, "relative_end": **********.084245, "duration": 2.3058228492736816, "duration_str": "2.31s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.084263, "relative_start": 2.3058409690856934, "end": 1749047523.654426, "relative_end": 3.0994415283203125e-06, "duration": 13.570163011550903, "duration_str": "13.57s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53580648, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 6, "templates": [{"name": "admin-views.dashboard-grocery", "param_count": null, "params": [], "start": **********.909608, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Admin-panel\\resources\\views/admin-views/dashboard-grocery.blade.phpadmin-views.dashboard-grocery", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Fadmin-views%2Fdashboard-grocery.blade.php&line=1", "ajax": false, "filename": "dashboard-grocery.blade.php", "line": "?"}}, {"name": "layouts.admin.app", "param_count": null, "params": [], "start": 1749047518.07098, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/app.blade.phplayouts.admin.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "layouts.admin.partials._front-settings", "param_count": null, "params": [], "start": 1749047518.295234, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_front-settings.blade.phplayouts.admin.partials._front-settings", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_front-settings.blade.php&line=1", "ajax": false, "filename": "_front-settings.blade.php", "line": "?"}}, {"name": "layouts.admin.partials._header", "param_count": null, "params": [], "start": 1749047518.297456, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_header.blade.phplayouts.admin.partials._header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_header.blade.php&line=1", "ajax": false, "filename": "_header.blade.php", "line": "?"}}, {"name": "layouts.admin.partials._sidebar_grocery", "param_count": null, "params": [], "start": 1749047520.177889, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.phplayouts.admin.partials._sidebar_grocery", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=1", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "?"}}, {"name": "layouts.admin.partials._footer", "param_count": null, "params": [], "start": 1749047522.749867, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_footer.blade.phplayouts.admin.partials._footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_footer.blade.php&line=1", "ajax": false, "filename": "_footer.blade.php", "line": "?"}}]}, "route": {"uri": "GET admin", "middleware": "web, admin, current-module", "controller": "App\\Http\\Controllers\\Admin\\DashboardController@dashboard", "as": "admin.dashboard", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "admin", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=217\" onclick=\"\">app/Http/Controllers/Admin/DashboardController.php:217-239</a>"}, "queries": {"nb_statements": 103, "nb_failed_statements": 0, "accumulated_duration": 10.77159, "accumulated_duration_str": "10.77s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 3 queries only show the query. Limit can be raised in the config. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select * from `business_settings` where `key` = 'system_language' limit 1", "type": "query", "params": [], "bindings": ["system_language"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "middleware", "name": "localization", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\Localization.php", "line": 24}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 78}], "start": **********.134827, "duration": 0.10659, "duration_str": "107ms", "memory": 0, "memory_str": null, "filename": "localization:24", "source": "middleware::localization:24", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FMiddleware%2FLocalization.php&line=24", "ajax": false, "filename": "Localization.php", "line": "24"}, "connection": "dease_test", "start_percent": 0, "width_percent": 0.99}, {"sql": "select * from `admins` where `id` = 5 limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": "middleware", "name": "admin", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\AdminMiddleware.php", "line": 21}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": "middleware", "name": "localization", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\Localization.php", "line": 60}], "start": **********.264202, "duration": 0.11176, "duration_str": "112ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "dease_test", "start_percent": 0.99, "width_percent": 1.038}, {"sql": "select * from `modules` where `status` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "middleware", "name": "current-module", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\CurrentModule.php", "line": 31}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "admin", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\AdminMiddleware.php", "line": 25}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "localization", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\Localization.php", "line": 60}], "start": **********.383495, "duration": 0.10287, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "current-module:31", "source": "middleware::current-module:31", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FMiddleware%2FCurrentModule.php&line=31", "ajax": false, "filename": "CurrentModule.php", "line": "31"}, "connection": "dease_test", "start_percent": 2.027, "width_percent": 0.955}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (1, 2, 5, 7) and `translations`.`translationable_type` = 'App\\Models\\Module' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\Module", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "current-module", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\CurrentModule.php", "line": 31}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": "middleware", "name": "admin", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\AdminMiddleware.php", "line": 25}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": "middleware", "name": "localization", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\Localization.php", "line": 60}], "start": **********.495068, "duration": 0.10423, "duration_str": "104ms", "memory": 0, "memory_str": null, "filename": "current-module:31", "source": "middleware::current-module:31", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FMiddleware%2FCurrentModule.php&line=31", "ajax": false, "filename": "CurrentModule.php", "line": "31"}, "connection": "dease_test", "start_percent": 2.982, "width_percent": 0.968}, {"sql": "select * from `users`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 447}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.613875, "duration": 0.*****************, "duration_str": "105ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:447", "source": "app/Http/Controllers/Admin/DashboardController.php:447", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=447", "ajax": false, "filename": "DashboardController.php", "line": "447"}, "connection": "dease_test", "start_percent": 3.95, "width_percent": 0.973}, {"sql": "select count(*) as aggregate from `orders` where `delivery_man_id` is null and `order_type` in ('delivery', 'parcel') and `order_status` not in ('delivered', 'failed', 'canceled', 'refund_requested', 'refund_request_canceled', 'refunded') and `module_id` = 1 and (`order_type` = 'take_away' or `order_type` = 'delivery') and ((created_at <> schedule_at and (`schedule_at` between '2025-06-04 15:31:50' and '2025-06-04 16:01:50') or `schedule_at` < '2025-06-04 15:31:50') or created_at = schedule_at) and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["delivery", "parcel", "delivered", "failed", "canceled", "refund_requested", "refund_request_canceled", "refunded", "1", "take_away", "delivery", "2025-06-04 15:31:50", "2025-06-04 16:01:50", "2025-06-04 15:31:50", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 485}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.728459, "duration": 0.10341, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:485", "source": "app/Http/Controllers/Admin/DashboardController.php:485", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=485", "ajax": false, "filename": "DashboardController.php", "line": "485"}, "connection": "dease_test", "start_percent": 4.923, "width_percent": 0.96}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'accepted' and `module_id` = 1 and (`order_type` = 'take_away' or `order_type` = 'delivery') and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["accepted", "1", "take_away", "delivery", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 486}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.838753, "duration": 0.10252, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:486", "source": "app/Http/Controllers/Admin/DashboardController.php:486", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=486", "ajax": false, "filename": "DashboardController.php", "line": "486"}, "connection": "dease_test", "start_percent": 5.883, "width_percent": 0.952}, {"sql": "select count(*) as aggregate from `orders` where `order_status` in ('confirmed', 'processing', 'handover') and `module_id` = 1 and (`order_type` = 'take_away' or `order_type` = 'delivery') and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["confirmed", "processing", "handover", "1", "take_away", "delivery", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 487}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.948303, "duration": 0.10392, "duration_str": "104ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:487", "source": "app/Http/Controllers/Admin/DashboardController.php:487", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=487", "ajax": false, "filename": "DashboardController.php", "line": "487"}, "connection": "dease_test", "start_percent": 6.835, "width_percent": 0.965}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'picked_up' and `module_id` = 1 and (`order_type` = 'take_away' or `order_type` = 'delivery') and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["picked_up", "1", "take_away", "delivery", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 488}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.058527, "duration": 0.*****************, "duration_str": "104ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:488", "source": "app/Http/Controllers/Admin/DashboardController.php:488", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=488", "ajax": false, "filename": "DashboardController.php", "line": "488"}, "connection": "dease_test", "start_percent": 7.799, "width_percent": 0.964}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'delivered' and `module_id` = 1 and (`order_type` = 'take_away' or `order_type` = 'delivery') and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["delivered", "1", "take_away", "delivery", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 489}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.169464, "duration": 0.10283, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:489", "source": "app/Http/Controllers/Admin/DashboardController.php:489", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=489", "ajax": false, "filename": "DashboardController.php", "line": "489"}, "connection": "dease_test", "start_percent": 8.763, "width_percent": 0.955}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'canceled' and `module_id` = 1 and (`order_type` = 'take_away' or `order_type` = 'delivery') and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["canceled", "1", "take_away", "delivery", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 490}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2787359, "duration": 0.10466, "duration_str": "105ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:490", "source": "app/Http/Controllers/Admin/DashboardController.php:490", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=490", "ajax": false, "filename": "DashboardController.php", "line": "490"}, "connection": "dease_test", "start_percent": 9.718, "width_percent": 0.972}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'failed' and `module_id` = 1 and (`order_type` = 'take_away' or `order_type` = 'delivery') and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["failed", "1", "take_away", "delivery", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 491}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.390398, "duration": 0.1039, "duration_str": "104ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:491", "source": "app/Http/Controllers/Admin/DashboardController.php:491", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=491", "ajax": false, "filename": "DashboardController.php", "line": "491"}, "connection": "dease_test", "start_percent": 10.69, "width_percent": 0.965}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'refunded' and `module_id` = 1 and (`order_type` = 'take_away' or `order_type` = 'delivery') and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["refunded", "1", "take_away", "delivery", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 492}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5017228, "duration": 0.10437, "duration_str": "104ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:492", "source": "app/Http/Controllers/Admin/DashboardController.php:492", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=492", "ajax": false, "filename": "DashboardController.php", "line": "492"}, "connection": "dease_test", "start_percent": 11.654, "width_percent": 0.969}, {"sql": "select count(*) as aggregate from `orders` where `module_id` = 1 and (`order_type` = 'take_away' or `order_type` = 'delivery') and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["1", "take_away", "delivery", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 493}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.614236, "duration": 0.10192, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:493", "source": "app/Http/Controllers/Admin/DashboardController.php:493", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=493", "ajax": false, "filename": "DashboardController.php", "line": "493"}, "connection": "dease_test", "start_percent": 12.623, "width_percent": 0.946}, {"sql": "select count(*) as aggregate from `items` where `module_id` = 1 and exists (select * from `stores` where `items`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["1", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 494}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7224321, "duration": 0.10284, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:494", "source": "app/Http/Controllers/Admin/DashboardController.php:494", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=494", "ajax": false, "filename": "DashboardController.php", "line": "494"}, "connection": "dease_test", "start_percent": 13.569, "width_percent": 0.955}, {"sql": "select count(*) as aggregate from `stores` where `module_id` = 1 and `zone_id` = 2", "type": "query", "params": [], "bindings": ["1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 495}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8328261, "duration": 0.10334, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:495", "source": "app/Http/Controllers/Admin/DashboardController.php:495", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=495", "ajax": false, "filename": "DashboardController.php", "line": "495"}, "connection": "dease_test", "start_percent": 14.524, "width_percent": 0.959}, {"sql": "select count(*) as aggregate from `orders` where `module_id` = 1 and date(`schedule_at`) >= '2025-05-05' and (`order_type` = 'take_away' or `order_type` = 'delivery') and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["1", "2025-05-05", "take_away", "delivery", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 497}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.94255, "duration": 0.10471, "duration_str": "105ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:497", "source": "app/Http/Controllers/Admin/DashboardController.php:497", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=497", "ajax": false, "filename": "DashboardController.php", "line": "497"}, "connection": "dease_test", "start_percent": 15.484, "width_percent": 0.972}, {"sql": "select count(*) as aggregate from `items` where `module_id` = 1 and date(`created_at`) >= '2025-05-05' and exists (select * from `stores` where `items`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["1", "2025-05-05", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 498}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.053509, "duration": 0.10664, "duration_str": "107ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:498", "source": "app/Http/Controllers/Admin/DashboardController.php:498", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=498", "ajax": false, "filename": "DashboardController.php", "line": "498"}, "connection": "dease_test", "start_percent": 16.456, "width_percent": 0.99}, {"sql": "select count(*) as aggregate from `stores` where `module_id` = 1 and date(`created_at`) >= '2025-05-05' and `zone_id` = 2", "type": "query", "params": [], "bindings": ["1", "2025-05-05", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 499}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.1661708, "duration": 0.10313, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:499", "source": "app/Http/Controllers/Admin/DashboardController.php:499", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=499", "ajax": false, "filename": "DashboardController.php", "line": "499"}, "connection": "dease_test", "start_percent": 17.446, "width_percent": 0.957}, {"sql": "select count(*) as aggregate from `users` where date(`created_at`) >= '2025-05-05'", "type": "query", "params": [], "bindings": ["2025-05-05"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 500}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 628}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.277422, "duration": 0.10364, "duration_str": "104ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:500", "source": "app/Http/Controllers/Admin/DashboardController.php:500", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=500", "ajax": false, "filename": "DashboardController.php", "line": "500"}, "connection": "dease_test", "start_percent": 18.403, "width_percent": 0.962}, {"sql": "select count(*) as aggregate from `users` where `id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 593}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 629}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.397074, "duration": 0.10241, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:593", "source": "app/Http/Controllers/Admin/DashboardController.php:593", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=593", "ajax": false, "filename": "DashboardController.php", "line": "593"}, "connection": "dease_test", "start_percent": 19.365, "width_percent": 0.951}, {"sql": "select count(*) as aggregate from `stores` where `module_id` = 1 and `id` is not null and `zone_id` = 2", "type": "query", "params": [], "bindings": ["1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 594}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 629}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.524508, "duration": 0.10197, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:594", "source": "app/Http/Controllers/Admin/DashboardController.php:594", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=594", "ajax": false, "filename": "DashboardController.php", "line": "594"}, "connection": "dease_test", "start_percent": 20.316, "width_percent": 0.947}, {"sql": "select count(*) as aggregate from `delivery_men` where `application_status` = 'approved' and `type` = 'zone_wise' and `zone_id` = 2", "type": "query", "params": [], "bindings": ["approved", "zone_wise", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 595}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 629}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.632627, "duration": 0.1037, "duration_str": "104ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:595", "source": "app/Http/Controllers/Admin/DashboardController.php:595", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=595", "ajax": false, "filename": "DashboardController.php", "line": "595"}, "connection": "dease_test", "start_percent": 21.263, "width_percent": 0.963}, {"sql": "select `store_id`, COUNT(store_id) as count from `wishlists` where exists (select * from `stores` where `wishlists`.`store_id` = `stores`.`id` and `zone_id` = 2) and exists (select * from `stores` where `wishlists`.`store_id` = `stores`.`id` and `module_id` = 1 and `zone_id` = 2) group by `store_id` order by `count` desc limit 6", "type": "query", "params": [], "bindings": ["2", "1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 643}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.7448678, "duration": 0.*****************, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:643", "source": "app/Http/Controllers/Admin/DashboardController.php:643", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=643", "ajax": false, "filename": "DashboardController.php", "line": "643"}, "connection": "dease_test", "start_percent": 22.225, "width_percent": 0.961}, {"sql": "select * from `items` where exists (select * from `stores` where `items`.`store_id` = `stores`.`id` and `module_id` = 1 and `zone_id` = 2) order by `order_count` desc limit 6", "type": "query", "params": [], "bindings": ["1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 657}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.85764, "duration": 0.10284, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:657", "source": "app/Http/Controllers/Admin/DashboardController.php:657", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=657", "ajax": false, "filename": "DashboardController.php", "line": "657"}, "connection": "dease_test", "start_percent": 23.186, "width_percent": 0.955}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (4, 5) and `translations`.`translationable_type` = 'App\\Models\\Item' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\Item", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 657}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.9666982, "duration": 0.10249, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:657", "source": "app/Http/Controllers/Admin/DashboardController.php:657", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=657", "ajax": false, "filename": "DashboardController.php", "line": "657"}, "connection": "dease_test", "start_percent": 24.141, "width_percent": 0.951}, {"sql": "select * from `items` where exists (select * from `stores` where `items`.`store_id` = `stores`.`id` and `module_id` = 1 and `zone_id` = 2) order by `rating_count` desc limit 6", "type": "query", "params": [], "bindings": ["1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 671}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.075087, "duration": 0.*****************, "duration_str": "105ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:671", "source": "app/Http/Controllers/Admin/DashboardController.php:671", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=671", "ajax": false, "filename": "DashboardController.php", "line": "671"}, "connection": "dease_test", "start_percent": 25.092, "width_percent": 0.979}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (4, 5) and `translations`.`translationable_type` = 'App\\Models\\Item' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\Item", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 671}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.186681, "duration": 0.10256, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:671", "source": "app/Http/Controllers/Admin/DashboardController.php:671", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=671", "ajax": false, "filename": "DashboardController.php", "line": "671"}, "connection": "dease_test", "start_percent": 26.071, "width_percent": 0.952}, {"sql": "select `delivery_men`.*, (select count(*) from `orders` where `delivery_men`.`id` = `orders`.`delivery_man_id` and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)) as `orders_count` from `delivery_men` where `type` = 'zone_wise' and `zone_id` = 2 order by `orders_count` desc limit 6", "type": "query", "params": [], "bindings": ["2", "2", "zone_wise", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 679}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.3016942, "duration": 0.1051, "duration_str": "105ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:679", "source": "app/Http/Controllers/Admin/DashboardController.php:679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=679", "ajax": false, "filename": "DashboardController.php", "line": "679"}, "connection": "dease_test", "start_percent": 27.023, "width_percent": 0.976}, {"sql": "select * from `users` order by `order_count` desc limit 6", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 686}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.413188, "duration": 0.1041, "duration_str": "104ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:686", "source": "app/Http/Controllers/Admin/DashboardController.php:686", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=686", "ajax": false, "filename": "DashboardController.php", "line": "686"}, "connection": "dease_test", "start_percent": 27.999, "width_percent": 0.966}, {"sql": "select * from `stores` where `module_id` = 1 and `zone_id` = 2 order by `order_count` desc limit 6", "type": "query", "params": [], "bindings": ["1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 696}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.523444, "duration": 0.10486, "duration_str": "105ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:696", "source": "app/Http/Controllers/Admin/DashboardController.php:696", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=696", "ajax": false, "filename": "DashboardController.php", "line": "696"}, "connection": "dease_test", "start_percent": 28.965, "width_percent": 0.973}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (6, 7, 21, 23) and `translations`.`translationable_type` = 'App\\Models\\Store' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\Store", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 696}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.634555, "duration": 0.11175, "duration_str": "112ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:696", "source": "app/Http/Controllers/Admin/DashboardController.php:696", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=696", "ajax": false, "filename": "DashboardController.php", "line": "696"}, "connection": "dease_test", "start_percent": 29.939, "width_percent": 1.037}, {"sql": "select sum(`order_amount`) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '01' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "01", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 744}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.842524, "duration": 0.10795, "duration_str": "108ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:744", "source": "app/Http/Controllers/Admin/DashboardController.php:744", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=744", "ajax": false, "filename": "DashboardController.php", "line": "744"}, "connection": "dease_test", "start_percent": 30.976, "width_percent": 1.002}, {"sql": "select sum(admin_commission + admin_expense - delivery_fee_comission) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '01' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "01", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 753}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.9588108, "duration": 0.10557, "duration_str": "106ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:753", "source": "app/Http/Controllers/Admin/DashboardController.php:753", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=753", "ajax": false, "filename": "DashboardController.php", "line": "753"}, "connection": "dease_test", "start_percent": 31.978, "width_percent": 0.98}, {"sql": "select sum(`delivery_fee_comission`) as aggregate from `order_transactions` where `module_id` = 1 and month(`created_at`) = '01' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["1", "01", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 761}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.071911, "duration": 0.10648, "duration_str": "106ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:761", "source": "app/Http/Controllers/Admin/DashboardController.php:761", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=761", "ajax": false, "filename": "DashboardController.php", "line": "761"}, "connection": "dease_test", "start_percent": 32.958, "width_percent": 0.989}, {"sql": "select sum(`order_amount`) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '02' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "02", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 744}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.1868641, "duration": 0.10545, "duration_str": "105ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:744", "source": "app/Http/Controllers/Admin/DashboardController.php:744", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=744", "ajax": false, "filename": "DashboardController.php", "line": "744"}, "connection": "dease_test", "start_percent": 33.947, "width_percent": 0.979}, {"sql": "select sum(admin_commission + admin_expense - delivery_fee_comission) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '02' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "02", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 753}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.298333, "duration": 0.10516, "duration_str": "105ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:753", "source": "app/Http/Controllers/Admin/DashboardController.php:753", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=753", "ajax": false, "filename": "DashboardController.php", "line": "753"}, "connection": "dease_test", "start_percent": 34.926, "width_percent": 0.976}, {"sql": "select sum(`delivery_fee_comission`) as aggregate from `order_transactions` where `module_id` = 1 and month(`created_at`) = '02' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["1", "02", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 761}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.411618, "duration": 0.1038, "duration_str": "104ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:761", "source": "app/Http/Controllers/Admin/DashboardController.php:761", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=761", "ajax": false, "filename": "DashboardController.php", "line": "761"}, "connection": "dease_test", "start_percent": 35.902, "width_percent": 0.964}, {"sql": "select sum(`order_amount`) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '03' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "03", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 744}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.5236158, "duration": 0.*****************, "duration_str": "112ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:744", "source": "app/Http/Controllers/Admin/DashboardController.php:744", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=744", "ajax": false, "filename": "DashboardController.php", "line": "744"}, "connection": "dease_test", "start_percent": 36.866, "width_percent": 1.039}, {"sql": "select sum(admin_commission + admin_expense - delivery_fee_comission) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '03' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "03", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 753}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.643022, "duration": 0.*****************, "duration_str": "104ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:753", "source": "app/Http/Controllers/Admin/DashboardController.php:753", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=753", "ajax": false, "filename": "DashboardController.php", "line": "753"}, "connection": "dease_test", "start_percent": 37.904, "width_percent": 0.966}, {"sql": "select sum(`delivery_fee_comission`) as aggregate from `order_transactions` where `module_id` = 1 and month(`created_at`) = '03' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["1", "03", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 761}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.753551, "duration": 0.11944, "duration_str": "119ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:761", "source": "app/Http/Controllers/Admin/DashboardController.php:761", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=761", "ajax": false, "filename": "DashboardController.php", "line": "761"}, "connection": "dease_test", "start_percent": 38.871, "width_percent": 1.109}, {"sql": "select sum(`order_amount`) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '04' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "04", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 744}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.878685, "duration": 0.10332, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:744", "source": "app/Http/Controllers/Admin/DashboardController.php:744", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=744", "ajax": false, "filename": "DashboardController.php", "line": "744"}, "connection": "dease_test", "start_percent": 39.979, "width_percent": 0.959}, {"sql": "select sum(admin_commission + admin_expense - delivery_fee_comission) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '04' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "04", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 753}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.990813, "duration": 0.11441, "duration_str": "114ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:753", "source": "app/Http/Controllers/Admin/DashboardController.php:753", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=753", "ajax": false, "filename": "DashboardController.php", "line": "753"}, "connection": "dease_test", "start_percent": 40.939, "width_percent": 1.062}, {"sql": "select sum(`delivery_fee_comission`) as aggregate from `order_transactions` where `module_id` = 1 and month(`created_at`) = '04' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["1", "04", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 761}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.115137, "duration": 0.10369, "duration_str": "104ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:761", "source": "app/Http/Controllers/Admin/DashboardController.php:761", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=761", "ajax": false, "filename": "DashboardController.php", "line": "761"}, "connection": "dease_test", "start_percent": 42.001, "width_percent": 0.963}, {"sql": "select sum(`order_amount`) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '05' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "05", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 744}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.228106, "duration": 0.10662, "duration_str": "107ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:744", "source": "app/Http/Controllers/Admin/DashboardController.php:744", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=744", "ajax": false, "filename": "DashboardController.php", "line": "744"}, "connection": "dease_test", "start_percent": 42.963, "width_percent": 0.99}, {"sql": "select sum(admin_commission + admin_expense - delivery_fee_comission) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '05' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "05", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 753}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.3417668, "duration": 0.10416, "duration_str": "104ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:753", "source": "app/Http/Controllers/Admin/DashboardController.php:753", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=753", "ajax": false, "filename": "DashboardController.php", "line": "753"}, "connection": "dease_test", "start_percent": 43.953, "width_percent": 0.967}, {"sql": "select sum(`delivery_fee_comission`) as aggregate from `order_transactions` where `module_id` = 1 and month(`created_at`) = '05' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["1", "05", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 761}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.452111, "duration": 0.10391, "duration_str": "104ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:761", "source": "app/Http/Controllers/Admin/DashboardController.php:761", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=761", "ajax": false, "filename": "DashboardController.php", "line": "761"}, "connection": "dease_test", "start_percent": 44.92, "width_percent": 0.965}, {"sql": "select sum(`order_amount`) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '06' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "06", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 744}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.564344, "duration": 0.10168, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:744", "source": "app/Http/Controllers/Admin/DashboardController.php:744", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=744", "ajax": false, "filename": "DashboardController.php", "line": "744"}, "connection": "dease_test", "start_percent": 45.885, "width_percent": 0.944}, {"sql": "select sum(admin_commission + admin_expense - delivery_fee_comission) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '06' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "06", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 753}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.672363, "duration": 0.10252, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:753", "source": "app/Http/Controllers/Admin/DashboardController.php:753", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=753", "ajax": false, "filename": "DashboardController.php", "line": "753"}, "connection": "dease_test", "start_percent": 46.829, "width_percent": 0.952}, {"sql": "select sum(`delivery_fee_comission`) as aggregate from `order_transactions` where `module_id` = 1 and month(`created_at`) = '06' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["1", "06", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 761}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.782244, "duration": 0.1015, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:761", "source": "app/Http/Controllers/Admin/DashboardController.php:761", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=761", "ajax": false, "filename": "DashboardController.php", "line": "761"}, "connection": "dease_test", "start_percent": 47.781, "width_percent": 0.942}, {"sql": "select sum(`order_amount`) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '07' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "07", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 744}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.890555, "duration": 0.10112, "duration_str": "101ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:744", "source": "app/Http/Controllers/Admin/DashboardController.php:744", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=744", "ajax": false, "filename": "DashboardController.php", "line": "744"}, "connection": "dease_test", "start_percent": 48.723, "width_percent": 0.939}, {"sql": "select sum(admin_commission + admin_expense - delivery_fee_comission) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '07' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "07", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 753}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.997786, "duration": 0.10503, "duration_str": "105ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:753", "source": "app/Http/Controllers/Admin/DashboardController.php:753", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=753", "ajax": false, "filename": "DashboardController.php", "line": "753"}, "connection": "dease_test", "start_percent": 49.662, "width_percent": 0.975}, {"sql": "select sum(`delivery_fee_comission`) as aggregate from `order_transactions` where `module_id` = 1 and month(`created_at`) = '07' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["1", "07", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 761}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.109319, "duration": 0.10237, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:761", "source": "app/Http/Controllers/Admin/DashboardController.php:761", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=761", "ajax": false, "filename": "DashboardController.php", "line": "761"}, "connection": "dease_test", "start_percent": 50.637, "width_percent": 0.95}, {"sql": "select sum(`order_amount`) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '08' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "08", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 744}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.217904, "duration": 0.10271, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:744", "source": "app/Http/Controllers/Admin/DashboardController.php:744", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=744", "ajax": false, "filename": "DashboardController.php", "line": "744"}, "connection": "dease_test", "start_percent": 51.587, "width_percent": 0.954}, {"sql": "select sum(admin_commission + admin_expense - delivery_fee_comission) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '08' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "08", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 753}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.327584, "duration": 0.10142, "duration_str": "101ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:753", "source": "app/Http/Controllers/Admin/DashboardController.php:753", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=753", "ajax": false, "filename": "DashboardController.php", "line": "753"}, "connection": "dease_test", "start_percent": 52.541, "width_percent": 0.942}, {"sql": "select sum(`delivery_fee_comission`) as aggregate from `order_transactions` where `module_id` = 1 and month(`created_at`) = '08' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["1", "08", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 761}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.4375808, "duration": 0.10118, "duration_str": "101ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:761", "source": "app/Http/Controllers/Admin/DashboardController.php:761", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=761", "ajax": false, "filename": "DashboardController.php", "line": "761"}, "connection": "dease_test", "start_percent": 53.482, "width_percent": 0.939}, {"sql": "select sum(`order_amount`) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '09' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "09", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 744}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.54499, "duration": 0.1028, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:744", "source": "app/Http/Controllers/Admin/DashboardController.php:744", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=744", "ajax": false, "filename": "DashboardController.php", "line": "744"}, "connection": "dease_test", "start_percent": 54.421, "width_percent": 0.954}, {"sql": "select sum(admin_commission + admin_expense - delivery_fee_comission) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '09' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "09", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 753}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.654737, "duration": 0.10107, "duration_str": "101ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:753", "source": "app/Http/Controllers/Admin/DashboardController.php:753", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=753", "ajax": false, "filename": "DashboardController.php", "line": "753"}, "connection": "dease_test", "start_percent": 55.376, "width_percent": 0.938}, {"sql": "select sum(`delivery_fee_comission`) as aggregate from `order_transactions` where `module_id` = 1 and month(`created_at`) = '09' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["1", "09", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 761}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.761843, "duration": 0.10303, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:761", "source": "app/Http/Controllers/Admin/DashboardController.php:761", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=761", "ajax": false, "filename": "DashboardController.php", "line": "761"}, "connection": "dease_test", "start_percent": 56.314, "width_percent": 0.956}, {"sql": "select sum(`order_amount`) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '10' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "10", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 744}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.8735979, "duration": 0.10138, "duration_str": "101ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:744", "source": "app/Http/Controllers/Admin/DashboardController.php:744", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=744", "ajax": false, "filename": "DashboardController.php", "line": "744"}, "connection": "dease_test", "start_percent": 57.271, "width_percent": 0.941}, {"sql": "select sum(admin_commission + admin_expense - delivery_fee_comission) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '10' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "10", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 753}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.9812899, "duration": 0.10155, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:753", "source": "app/Http/Controllers/Admin/DashboardController.php:753", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=753", "ajax": false, "filename": "DashboardController.php", "line": "753"}, "connection": "dease_test", "start_percent": 58.212, "width_percent": 0.943}, {"sql": "select sum(`delivery_fee_comission`) as aggregate from `order_transactions` where `module_id` = 1 and month(`created_at`) = '10' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["1", "10", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 761}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.090231, "duration": 0.11094, "duration_str": "111ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:761", "source": "app/Http/Controllers/Admin/DashboardController.php:761", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=761", "ajax": false, "filename": "DashboardController.php", "line": "761"}, "connection": "dease_test", "start_percent": 59.155, "width_percent": 1.03}, {"sql": "select sum(`order_amount`) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '11' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "11", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 744}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.207469, "duration": 0.10221, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:744", "source": "app/Http/Controllers/Admin/DashboardController.php:744", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=744", "ajax": false, "filename": "DashboardController.php", "line": "744"}, "connection": "dease_test", "start_percent": 60.185, "width_percent": 0.949}, {"sql": "select sum(admin_commission + admin_expense - delivery_fee_comission) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '11' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "11", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 753}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.316892, "duration": 0.*****************, "duration_str": "111ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:753", "source": "app/Http/Controllers/Admin/DashboardController.php:753", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=753", "ajax": false, "filename": "DashboardController.php", "line": "753"}, "connection": "dease_test", "start_percent": 61.133, "width_percent": 1.03}, {"sql": "select sum(`delivery_fee_comission`) as aggregate from `order_transactions` where `module_id` = 1 and month(`created_at`) = '11' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["1", "11", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 761}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.4333968, "duration": 0.*****************, "duration_str": "111ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:761", "source": "app/Http/Controllers/Admin/DashboardController.php:761", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=761", "ajax": false, "filename": "DashboardController.php", "line": "761"}, "connection": "dease_test", "start_percent": 62.164, "width_percent": 1.026}, {"sql": "select sum(`order_amount`) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '12' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "12", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 744}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.553416, "duration": 0.10258, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:744", "source": "app/Http/Controllers/Admin/DashboardController.php:744", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=744", "ajax": false, "filename": "DashboardController.php", "line": "744"}, "connection": "dease_test", "start_percent": 63.19, "width_percent": 0.952}, {"sql": "select sum(admin_commission + admin_expense - delivery_fee_comission) as aggregate from `order_transactions` where (`status` not in ('refunded_with_delivery_charge', 'refunded_without_delivery_charge') or `status` is null) and `module_id` = 1 and month(`created_at`) = '12' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["refunded_with_delivery_charge", "refunded_without_delivery_charge", "1", "12", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 753}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.663958, "duration": 0.10199, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:753", "source": "app/Http/Controllers/Admin/DashboardController.php:753", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=753", "ajax": false, "filename": "DashboardController.php", "line": "753"}, "connection": "dease_test", "start_percent": 64.142, "width_percent": 0.947}, {"sql": "select sum(`delivery_fee_comission`) as aggregate from `order_transactions` where `module_id` = 1 and month(`created_at`) = '12' and year(`created_at`) = '2025'", "type": "query", "params": [], "bindings": ["1", "12", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 761}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 228}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.771839, "duration": 0.11363, "duration_str": "114ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:761", "source": "app/Http/Controllers/Admin/DashboardController.php:761", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=761", "ajax": false, "filename": "DashboardController.php", "line": "761"}, "connection": "dease_test", "start_percent": 65.089, "width_percent": 1.055}, {"sql": "select * from `business_settings` where (`key` = 'business_name') limit 1", "type": "query", "params": [], "bindings": ["business_name"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "admin-views.dashboard-grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/admin-views/dashboard-grocery.blade.php", "line": 1}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.912143, "duration": 0.10401, "duration_str": "104ms", "memory": 0, "memory_str": null, "filename": "admin-views.dashboard-grocery:1", "source": "view::admin-views.dashboard-grocery:1", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Fadmin-views%2Fdashboard-grocery.blade.php&line=1", "ajax": false, "filename": "dashboard-grocery.blade.php", "line": "1"}, "connection": "dease_test", "start_percent": 66.144, "width_percent": 0.966}, {"sql": "select * from `business_settings` where `key` = 'country' limit 1", "type": "query", "params": [], "bindings": ["country"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.app", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/app.blade.php", "line": 4}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047518.073947, "duration": 0.10287, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.app:4", "source": "view::layouts.admin.app:4", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fapp.blade.php&line=4", "ajax": false, "filename": "app.blade.php", "line": "4"}, "connection": "dease_test", "start_percent": 67.109, "width_percent": 0.955}, {"sql": "select * from `business_settings` where (`key` = 'icon') limit 1", "type": "query", "params": [], "bindings": ["icon"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.app", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/app.blade.php", "line": 15}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047518.1841009, "duration": 0.10292, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.app:15", "source": "view::layouts.admin.app:15", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fapp.blade.php&line=15", "ajax": false, "filename": "app.blade.php", "line": "15"}, "connection": "dease_test", "start_percent": 68.064, "width_percent": 0.955}, {"sql": "select * from `admin_roles` where `admin_roles`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\CentralLogics\\helpers.php", "line": 2196}, {"index": 22, "namespace": "view", "name": "layouts.admin.partials._header", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_header.blade.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749047518.323915, "duration": 0.10405, "duration_str": "104ms", "memory": 0, "memory_str": null, "filename": "helpers.php:2196", "source": "app/CentralLogics/helpers.php:2196", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FCentralLogics%2Fhelpers.php&line=2196", "ajax": false, "filename": "helpers.php", "line": "2196"}, "connection": "dease_test", "start_percent": 69.02, "width_percent": 0.966}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (4) and `translations`.`translationable_type` = 'App\\Models\\AdminRole' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\AdminRole", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\CentralLogics\\helpers.php", "line": 2196}, {"index": 27, "namespace": "view", "name": "layouts.admin.partials._header", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_header.blade.php", "line": 57}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749047518.4341729, "duration": 0.10171, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "helpers.php:2196", "source": "app/CentralLogics/helpers.php:2196", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FCentralLogics%2Fhelpers.php&line=2196", "ajax": false, "filename": "helpers.php", "line": "2196"}, "connection": "dease_test", "start_percent": 69.986, "width_percent": 0.944}, {"sql": "select count(*) as aggregate from `conversations` where (`sender_type` = 'admin' or `receiver_type` = 'admin') and `unread_message_count` > '0'", "type": "query", "params": [], "bindings": ["admin", "admin", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._header", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_header.blade.php", "line": 115}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047518.576651, "duration": 0.10307, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._header:115", "source": "view::layouts.admin.partials._header:115", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_header.blade.php&line=115", "ajax": false, "filename": "_header.blade.php", "line": "115"}, "connection": "dease_test", "start_percent": 70.93, "width_percent": 0.957}, {"sql": "select * from `business_settings` where `key` = 'system_language' limit 1", "type": "query", "params": [], "bindings": ["system_language"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._header", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_header.blade.php", "line": 125}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047518.685589, "duration": 0.10229, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._header:125", "source": "view::layouts.admin.partials._header:125", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_header.blade.php&line=125", "ajax": false, "filename": "_header.blade.php", "line": "125"}, "connection": "dease_test", "start_percent": 71.887, "width_percent": 0.95}, {"sql": "select * from `modules` where `modules`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "layouts.admin.partials._header", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_header.blade.php", "line": 159}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047518.7947152, "duration": 0.1023, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._header:159", "source": "view::layouts.admin.partials._header:159", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_header.blade.php&line=159", "ajax": false, "filename": "_header.blade.php", "line": "159"}, "connection": "dease_test", "start_percent": 72.837, "width_percent": 0.95}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (1) and `translations`.`translationable_type` = 'App\\Models\\Module' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\Module", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": "view", "name": "layouts.admin.partials._header", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_header.blade.php", "line": 159}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047518.9053228, "duration": 0.10945999999999999, "duration_str": "109ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._header:159", "source": "view::layouts.admin.partials._header:159", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_header.blade.php&line=159", "ajax": false, "filename": "_header.blade.php", "line": "159"}, "connection": "dease_test", "start_percent": 73.786, "width_percent": 1.016}, {"sql": "select * from `modules` where exists (select * from `zones` inner join `module_zone` on `zones`.`id` = `module_zone`.`zone_id` where `modules`.`id` = `module_zone`.`module_id` and `zone_id` = 2 and `zones`.`id` = 2) and `status` = 1", "type": "query", "params": [], "bindings": ["2", "2", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "layouts.admin.partials._header", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_header.blade.php", "line": 178}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047519.673814, "duration": 0.10473, "duration_str": "105ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._header:178", "source": "view::layouts.admin.partials._header:178", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_header.blade.php&line=178", "ajax": false, "filename": "_header.blade.php", "line": "178"}, "connection": "dease_test", "start_percent": 74.802, "width_percent": 0.972}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (1, 2, 5, 7) and `translations`.`translationable_type` = 'App\\Models\\Module' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\Module", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "layouts.admin.partials._header", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_header.blade.php", "line": 178}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047519.786306, "duration": 0.10542, "duration_str": "105ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._header:178", "source": "view::layouts.admin.partials._header:178", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_header.blade.php&line=178", "ajax": false, "filename": "_header.blade.php", "line": "178"}, "connection": "dease_test", "start_percent": 75.775, "width_percent": 0.979}, {"sql": "select * from `business_settings` where (`key` = 'logo') limit 1", "type": "query", "params": [], "bindings": ["logo"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 6}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047520.1835082, "duration": 0.10368000000000001, "duration_str": "104ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:6", "source": "view::layouts.admin.partials._sidebar_grocery:6", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=6", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "6"}, "connection": "dease_test", "start_percent": 76.753, "width_percent": 0.963}, {"sql": "select count(*) as aggregate from `orders` where (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1 and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["take_away", "delivery", "1", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 97}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047520.4601629, "duration": 0.10521, "duration_str": "105ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:97", "source": "view::layouts.admin.partials._sidebar_grocery:97", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=97", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "97"}, "connection": "dease_test", "start_percent": 77.716, "width_percent": 0.977}, {"sql": "select count(*) as aggregate from `orders` where created_at <> schedule_at and `scheduled` = '1' and (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1 and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["1", "take_away", "delivery", "1", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 110}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047520.580806, "duration": 0.10332, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:110", "source": "view::layouts.admin.partials._sidebar_grocery:110", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=110", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "110"}, "connection": "dease_test", "start_percent": 78.693, "width_percent": 0.959}, {"sql": "select * from `business_settings` where `key` = 'first_order_status' limit 1", "type": "query", "params": [], "bindings": ["first_order_status"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 116}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047520.6897678, "duration": 0.10537, "duration_str": "105ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:116", "source": "view::layouts.admin.partials._sidebar_grocery:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=116", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "116"}, "connection": "dease_test", "start_percent": 79.652, "width_percent": 0.978}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'initial' and ((created_at <> schedule_at and (`schedule_at` between '2025-06-04 15:32:00' and '2025-06-04 16:02:00') or `schedule_at` < '2025-06-04 15:32:00') or created_at = schedule_at) and (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1 and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["initial", "2025-06-04 15:32:00", "2025-06-04 16:02:00", "2025-06-04 15:32:00", "take_away", "delivery", "1", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 126}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047520.8095028, "duration": 0.10595, "duration_str": "106ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:126", "source": "view::layouts.admin.partials._sidebar_grocery:126", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=126", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "126"}, "connection": "dease_test", "start_percent": 80.63, "width_percent": 0.984}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'pending' and ((created_at <> schedule_at and (`schedule_at` between '2025-06-04 15:32:00' and '2025-06-04 16:02:00') or `schedule_at` < '2025-06-04 15:32:00') or created_at = schedule_at) and (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1 and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["pending", "2025-06-04 15:32:00", "2025-06-04 16:02:00", "2025-06-04 15:32:00", "take_away", "delivery", "1", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 140}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047520.9328969, "duration": 0.10540000000000001, "duration_str": "105ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:140", "source": "view::layouts.admin.partials._sidebar_grocery:140", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=140", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "140"}, "connection": "dease_test", "start_percent": 81.614, "width_percent": 0.978}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'accepted' and ((created_at <> schedule_at and (`schedule_at` between '2025-06-04 15:32:01' and '2025-06-04 16:02:01') or `schedule_at` < '2025-06-04 15:32:01') or created_at = schedule_at) and (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1 and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["accepted", "2025-06-04 15:32:01", "2025-06-04 16:02:01", "2025-06-04 15:32:01", "take_away", "delivery", "1", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 154}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047521.053583, "duration": 0.10466, "duration_str": "105ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:154", "source": "view::layouts.admin.partials._sidebar_grocery:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=154", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "154"}, "connection": "dease_test", "start_percent": 82.592, "width_percent": 0.972}, {"sql": "select count(*) as aggregate from `orders` where `order_status` in ('confirmed', 'processing', 'handover') and ((created_at <> schedule_at and (`schedule_at` between '2025-06-04 15:32:01' and '2025-06-04 16:02:01') or `schedule_at` < '2025-06-04 15:32:01') or created_at = schedule_at) and (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1 and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["confirmed", "processing", "handover", "2025-06-04 15:32:01", "2025-06-04 16:02:01", "2025-06-04 15:32:01", "take_away", "delivery", "1", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 167}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047521.1728091, "duration": 0.*****************, "duration_str": "105ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:167", "source": "view::layouts.admin.partials._sidebar_grocery:167", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=167", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "167"}, "connection": "dease_test", "start_percent": 83.564, "width_percent": 0.979}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'picked_up' and ((created_at <> schedule_at and (`schedule_at` between '2025-06-04 15:32:01' and '2025-06-04 16:02:01') or `schedule_at` < '2025-06-04 15:32:01') or created_at = schedule_at) and (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1 and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["picked_up", "2025-06-04 15:32:01", "2025-06-04 16:02:01", "2025-06-04 15:32:01", "take_away", "delivery", "1", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047521.292623, "duration": 0.10373, "duration_str": "104ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:180", "source": "view::layouts.admin.partials._sidebar_grocery:180", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=180", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "180"}, "connection": "dease_test", "start_percent": 84.543, "width_percent": 0.963}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'delivered' and (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1 and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["delivered", "take_away", "delivery", "1", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 193}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047521.410703, "duration": 0.*****************, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:193", "source": "view::layouts.admin.partials._sidebar_grocery:193", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=193", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "193"}, "connection": "dease_test", "start_percent": 85.506, "width_percent": 0.961}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'canceled' and (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1 and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["canceled", "take_away", "delivery", "1", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 206}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047521.528238, "duration": 0.10493000000000001, "duration_str": "105ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:206", "source": "view::layouts.admin.partials._sidebar_grocery:206", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=206", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "206"}, "connection": "dease_test", "start_percent": 86.466, "width_percent": 0.974}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'failed' and (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1 and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["failed", "take_away", "delivery", "1", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 219}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047521.647698, "duration": 0.10461, "duration_str": "105ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:219", "source": "view::layouts.admin.partials._sidebar_grocery:219", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=219", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "219"}, "connection": "dease_test", "start_percent": 87.44, "width_percent": 0.971}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'refunded' and (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1 and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["refunded", "take_away", "delivery", "1", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 232}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047521.766314, "duration": 0.10393000000000001, "duration_str": "104ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:232", "source": "view::layouts.admin.partials._sidebar_grocery:232", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=232", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "232"}, "connection": "dease_test", "start_percent": 88.411, "width_percent": 0.965}, {"sql": "select count(*) as aggregate from `orders` where exists (select * from `offline_payments` where `orders`.`id` = `offline_payments`.`order_id`) and (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1 and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["take_away", "delivery", "1", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 246}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047521.885775, "duration": 0.10529000000000001, "duration_str": "105ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:246", "source": "view::layouts.admin.partials._sidebar_grocery:246", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=246", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "246"}, "connection": "dease_test", "start_percent": 89.376, "width_percent": 0.977}, {"sql": "select count(*) as aggregate from `orders` where `order_status` = 'refund_requested' and (`order_type` = 'take_away' or `order_type` = 'delivery') and `module_id` = 1 and exists (select * from `stores` where `orders`.`store_id` = `stores`.`id` and `zone_id` = 2 and `zone_id` = 2)", "type": "query", "params": [], "bindings": ["refund_requested", "take_away", "delivery", "1", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 279}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047522.014546, "duration": 0.10348, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:279", "source": "view::layouts.admin.partials._sidebar_grocery:279", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=279", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "279"}, "connection": "dease_test", "start_percent": 90.354, "width_percent": 0.961}, {"sql": "select * from `business_settings` where `key` = 'product_approval' limit 1", "type": "query", "params": [], "bindings": ["product_approval"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\CentralLogics\\helpers.php", "line": 3303}, {"index": 17, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 482}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749047522.297462, "duration": 0.10468000000000001, "duration_str": "105ms", "memory": 0, "memory_str": null, "filename": "helpers.php:3303", "source": "app/CentralLogics/helpers.php:3303", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FCentralLogics%2Fhelpers.php&line=3303", "ajax": false, "filename": "helpers.php", "line": "3303"}, "connection": "dease_test", "start_percent": 91.314, "width_percent": 0.972}, {"sql": "select * from `stores` where exists (select * from `vendors` where `stores`.`vendor_id` = `vendors`.`id` and `status` is null) and `module_id` = 1 and `zone_id` = 2", "type": "query", "params": [], "bindings": ["1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "layouts.admin.partials._sidebar_grocery", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_sidebar_grocery.blade.php", "line": 529}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047522.4602082, "duration": 0.10315, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._sidebar_grocery:529", "source": "view::layouts.admin.partials._sidebar_grocery:529", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_sidebar_grocery.blade.php&line=529", "ajax": false, "filename": "_sidebar_grocery.blade.php", "line": "529"}, "connection": "dease_test", "start_percent": 92.286, "width_percent": 0.958}, {"sql": "select * from `business_settings` where (`key` = 'business_name') limit 1", "type": "query", "params": [], "bindings": ["business_name"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._footer", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_footer.blade.php", "line": 5}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047522.752209, "duration": 0.10239, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._footer:5", "source": "view::layouts.admin.partials._footer:5", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_footer.blade.php&line=5", "ajax": false, "filename": "_footer.blade.php", "line": "5"}, "connection": "dease_test", "start_percent": 93.244, "width_percent": 0.951}, {"sql": "select * from `business_settings` where (`key` = 'footer_text') limit 1", "type": "query", "params": [], "bindings": ["footer_text"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.admin.partials._footer", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/partials/_footer.blade.php", "line": 6}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047522.860094, "duration": 0.10518000000000001, "duration_str": "105ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.partials._footer:6", "source": "view::layouts.admin.partials._footer:6", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fpartials%2F_footer.blade.php&line=6", "ajax": false, "filename": "_footer.blade.php", "line": "6"}, "connection": "dease_test", "start_percent": 94.194, "width_percent": 0.976}, {"sql": "select * from `modules` where `status` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "layouts.admin.app", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/app.blade.php", "line": 257}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047523.015185, "duration": 0.10604000000000001, "duration_str": "106ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.app:257", "source": "view::layouts.admin.app:257", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fapp.blade.php&line=257", "ajax": false, "filename": "app.blade.php", "line": "257"}, "connection": "dease_test", "start_percent": 95.171, "width_percent": 0.984}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (1, 2, 5, 7) and `translations`.`translationable_type` = 'App\\Models\\Module' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\Module", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "layouts.admin.app", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/admin/app.blade.php", "line": 257}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1749047523.128482, "duration": 0.10481, "duration_str": "105ms", "memory": 0, "memory_str": null, "filename": "layouts.admin.app:257", "source": "view::layouts.admin.app:257", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Fadmin%2Fapp.blade.php&line=257", "ajax": false, "filename": "app.blade.php", "line": "257"}, "connection": "dease_test", "start_percent": 96.155, "width_percent": 0.973}, {"sql": "select * from `business_settings` where `key` = 'fcm_credentials' limit 1", "type": "query", "params": [], "bindings": null, "hints": null, "show_copy": false, "backtrace": [], "start": 1749047523.3138628, "duration": 0.10329, "duration_str": "103ms", "memory": 0, "memory_str": null, "filename": "", "source": "", "xdebug_link": null, "connection": "dease_test", "start_percent": 97.128, "width_percent": 0.959}, {"sql": "select * from `business_settings` where `key` = 'order_notification_type' limit 1", "type": "query", "params": [], "bindings": null, "hints": null, "show_copy": false, "backtrace": [], "start": 1749047523.417789, "duration": 0.10239, "duration_str": "102ms", "memory": 0, "memory_str": null, "filename": "", "source": "", "xdebug_link": null, "connection": "dease_test", "start_percent": 98.087, "width_percent": 0.951}, {"sql": "select * from `business_settings` where `key` = 'admin_order_notification' limit 1", "type": "query", "params": [], "bindings": null, "hints": null, "show_copy": false, "backtrace": [], "start": 1749047523.520637, "duration": 0.10364, "duration_str": "104ms", "memory": 0, "memory_str": null, "filename": "", "source": "", "xdebug_link": null, "connection": "dease_test", "start_percent": 99.038, "width_percent": 0.962}]}, "models": {"data": {"App\\Models\\Translation": {"value": 43, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FTranslation.php&line=1", "ajax": false, "filename": "Translation.php", "line": "?"}}, "App\\Models\\User": {"value": 30, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\BusinessSetting": {"value": 13, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FBusinessSetting.php&line=1", "ajax": false, "filename": "BusinessSetting.php", "line": "?"}}, "App\\Models\\Module": {"value": 13, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FModule.php&line=1", "ajax": false, "filename": "Module.php", "line": "?"}}, "App\\Models\\DeliveryMan": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FDeliveryMan.php&line=1", "ajax": false, "filename": "DeliveryMan.php", "line": "?"}}, "App\\Models\\Item": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FItem.php&line=1", "ajax": false, "filename": "Item.php", "line": "?"}}, "App\\Models\\Store": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}, "App\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "App\\Models\\AdminRole": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FAdminRole.php&line=1", "ajax": false, "filename": "AdminRole.php", "line": "?"}}}, "count": 115, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VLPBp8OZ5DPQQjor5cQrLq4YgrNA8oclmzN41QBj", "landing_site_direction": "ltr", "six_captcha": "HGEMZ", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/Admin-panel/admin\"\n]", "site_direction": "ltr", "dash_params": "array:6 [\n  \"zone_id\" => \"all\"\n  \"module_id\" => 1\n  \"statistics_type\" => \"overall\"\n  \"user_overview\" => \"overall\"\n  \"commission_overview\" => \"this_year\"\n  \"business_overview\" => \"overall\"\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "5", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/admin", "status_code": "<pre class=sf-dump id=sf-dump-1297200108 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1297200108\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-960380627 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-960380627\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2071764383 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2071764383\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2138977509 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"49 characters\">http://localhost/Admin-panel/login/admin-employee</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">en-US,en;q=0.9,fr-DZ;q=0.8,fr;q=0.7,ar-DZ;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1274 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ik5jRlo5KzllMDgvcERoQklrT3VUYkE9PSIsInZhbHVlIjoiVVFoUkF1aUFCQ1pIQjcxVWRKN0Y0bnJabkgrWFEwckhLVmF1NnQ3YkpPblJZVmhCL05ZK2h4c3Q3VGZVeWErRkNGeDJDY0RFMCt6NnM1d1E4cVlxRUpKN3lCUVFheWxUaXlCVWdpb21pRThQbmRyU3E0MEpvQlVoYVo3a25SeDNnaGlZR3B6YTJFc3J0KytQRlpHSE1CakpzbGR2OXRPeTN6ZmNUWmI4N2xnUlZCRW1LeHlPWElHcmthb3NkSnJ0R2NqUlUwTWZseW1zVzgrcTg3WEpzajFpWGdUMFZQMGd0eGN6ZlFDUWFBbz0iLCJtYWMiOiI1MjUzMzQ0OGQ1NTE1NTBkNzI5ZjQwZWE4OTFiNDJiNTM2ZjBiYjg4NTkxNDBhZjI4MjEwZmIwMzg3NzQ2ZWNhIiwidGFnIjoiIn0%3D; SL_G_WPT_TO=fr; XSRF-TOKEN=eyJpdiI6Im8xSEhEVFQvSGk0RGxOOHVkeWdkb0E9PSIsInZhbHVlIjoiRHlqdVZZRG05aHM3UTJacUo1eUJkN0svTlVTSGFEQjczaWc4QVByMTdaVTdTdUhhaWsxR2x5eXJ1ZG1nMTVzMXB4Y3RhWUFPalFzUlF2MkhCTkZUZ1kvYitra1MzVG9GQlJrbzZ3b1lrNS85My9UODMzU0ZXc0RkQ0krR01IeUwiLCJtYWMiOiI3NmQxYzg5OGQzZjIxYmRhNTIwYTg0OWI0Y2Q5ZGU0MmNhYzgxMDY1ZDc1NGI1MWMxNjhkMmQxMWNiZjdiMjI5IiwidGFnIjoiIn0%3D; deaseapp_session=eyJpdiI6IldKL1ZYZm5rT3pFNUEzdEZFMFp2UWc9PSIsInZhbHVlIjoiSmdYVXJyRWRCQnY2SFZTM2drQlFJcHNySnpyYWYvY2pGSXJBaUZIVEZjR2hYQm13dlZWUVY2Z3lVcS9TcnNyVzBxcjk0L0F6MDBFOXc5Zmx2R1RYMzR1alhTRmpPUGNpSDJyRzVWUzcrb3hRZXVLRm5QcXB1VVcwSHpGTXZiUFciLCJtYWMiOiI3ZjkwM2IyNzc5MWMzOWNlNTQ1MzE0NmUxMWM3Nzg2NGZhYzY5NjdhZWUxMzgxYzJhZDRmNTIyMWEzNTVlODQ3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2138977509\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-76350967 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>SL_G_WPT_TO</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VLPBp8OZ5DPQQjor5cQrLq4YgrNA8oclmzN41QBj</span>\"\n  \"<span class=sf-dump-key>deaseapp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XMiuBr1yOXAurhi4cy3mBl1ZgZPaCZMy8yfDELNe</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-76350967\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1203902471 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 04 Jun 2025 14:31:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"414 characters\">XSRF-TOKEN=eyJpdiI6IjFSelhBT3dyN0g2djdhYnJ3WlBSV2c9PSIsInZhbHVlIjoiYkJ1UmtpeEFRQ0szd3RCTnFhWEFhRGpzRmM2ZGk5V1NXVFQ4b20wTWtjR1NOM0dreEZybnVqRTNwTFpHbnp0dDh1QzFrNHlMTUFyY1FvWWlGQ0lNNHBXNlFHcEU5Zjl0cW8wNXRTRDNwVDVmN3l5QldXQ1NvS3plOHQ1YnNyRmMiLCJtYWMiOiI5OWIyNmYzODUxZjBkOWMyOGQyYmIyY2U1OGViZjYyY2EyZDQwYTM0MjdhZThjZDg0N2I3Yjg0NTNlMTgxMTRhIiwidGFnIjoiIn0%3D; expires=Wed, 04 Jun 2025 16:32:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"430 characters\">deaseapp_session=eyJpdiI6ImtVMVJmQkVVc2pjbTRlQWIyRTB1Q2c9PSIsInZhbHVlIjoiWU5TaUZvcmZCRkRNQUJoV21CeEFHdTlvSUZPNEtPbTNuQXpMVjZkcDk2UGtLM3NMR29qRXdoTGMxYVRneWJMc1pWaTlFMG1Ka0pPM0ZFK3FnU1Y5bmFGZ2NGNkc4cmplY0lGeTJLd1RZbThEdHlFcng0dEVIN2VQMktDL1E0blMiLCJtYWMiOiJlYmYxMTIyYzBiZjNjM2I0OTI1NWFhZGZlOGY1NmY2Mjk0MjY3Mzg1ZDNkOTYzMzdjNDVhMTViMDhiZDAyZTI3IiwidGFnIjoiIn0%3D; expires=Wed, 04 Jun 2025 16:32:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjFSelhBT3dyN0g2djdhYnJ3WlBSV2c9PSIsInZhbHVlIjoiYkJ1UmtpeEFRQ0szd3RCTnFhWEFhRGpzRmM2ZGk5V1NXVFQ4b20wTWtjR1NOM0dreEZybnVqRTNwTFpHbnp0dDh1QzFrNHlMTUFyY1FvWWlGQ0lNNHBXNlFHcEU5Zjl0cW8wNXRTRDNwVDVmN3l5QldXQ1NvS3plOHQ1YnNyRmMiLCJtYWMiOiI5OWIyNmYzODUxZjBkOWMyOGQyYmIyY2U1OGViZjYyY2EyZDQwYTM0MjdhZThjZDg0N2I3Yjg0NTNlMTgxMTRhIiwidGFnIjoiIn0%3D; expires=Wed, 04-Jun-2025 16:32:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">deaseapp_session=eyJpdiI6ImtVMVJmQkVVc2pjbTRlQWIyRTB1Q2c9PSIsInZhbHVlIjoiWU5TaUZvcmZCRkRNQUJoV21CeEFHdTlvSUZPNEtPbTNuQXpMVjZkcDk2UGtLM3NMR29qRXdoTGMxYVRneWJMc1pWaTlFMG1Ka0pPM0ZFK3FnU1Y5bmFGZ2NGNkc4cmplY0lGeTJLd1RZbThEdHlFcng0dEVIN2VQMktDL1E0blMiLCJtYWMiOiJlYmYxMTIyYzBiZjNjM2I0OTI1NWFhZGZlOGY1NmY2Mjk0MjY3Mzg1ZDNkOTYzMzdjNDVhMTViMDhiZDAyZTI3IiwidGFnIjoiIn0%3D; expires=Wed, 04-Jun-2025 16:32:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1203902471\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1313045072 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VLPBp8OZ5DPQQjor5cQrLq4YgrNA8oclmzN41QBj</span>\"\n  \"<span class=sf-dump-key>landing_site_direction</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ltr</span>\"\n  \"<span class=sf-dump-key>six_captcha</span>\" => \"<span class=sf-dump-str title=\"5 characters\">HGEMZ</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/Admin-panel/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>site_direction</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ltr</span>\"\n  \"<span class=sf-dump-key>dash_params</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>zone_id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">all</span>\"\n    \"<span class=sf-dump-key>module_id</span>\" => <span class=sf-dump-num>1</span>\n    \"<span class=sf-dump-key>statistics_type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">overall</span>\"\n    \"<span class=sf-dump-key>user_overview</span>\" => \"<span class=sf-dump-str title=\"7 characters\">overall</span>\"\n    \"<span class=sf-dump-key>commission_overview</span>\" => \"<span class=sf-dump-str title=\"9 characters\">this_year</span>\"\n    \"<span class=sf-dump-key>business_overview</span>\" => \"<span class=sf-dump-str title=\"7 characters\">overall</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1313045072\", {\"maxDepth\":0})</script>\n"}}